<template>
  <div class="chat-message-container">
    <div class="chat-message">
      <div class="avatar avatar-user">
        <img src="@/assets/user.svg" alt="User Avatar" />
      </div>
      <div class="message-content">
        <div class="message-title">
          <slot></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UserMessage'
}
</script>

<style lang="scss" scoped>
.chat-message-container {
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
  border: 1px solid #E8E8E8;
  border-radius: 4px;
  padding: 15px 20px;
  margin-left: auto;
  margin-bottom: 20px;
  width: fit-content;
}

.chat-message {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  flex-direction: row-reverse;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;

  &.avatar-user {
    background: linear-gradient(to bottom right, #10B981, #06B6D4, #3B82F6);
    overflow: hidden;

    img {
      width: 20px;
      height: 20px;
    }
  }
}

.message-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex-grow: 1;
  text-align: left;

  .message-title {
    font-weight: bold;
    font-size: 16px;
    color: #313233;
    line-height: 1.6;
    word-break: break-word;
  }
}
</style>
