<template>
  <div class="message-actions">
    <div class="action-button copy-button" @click="handleCopy">
      <img src="../../chat-v3/icons/copy.svg" alt="Copy" width="12" height="14"/>
      <span>复制</span>
    </div>
    <div class="action-button" @click="handleDelete">
      <img src="../../chat-v3/icons/delete.svg" alt="Delete" width="12" height="14"/>
      <span>删除</span>
    </div>

    <div
      class="action-button"
      :class="{ active: isVoicePlaying }"
      @click="handleVoicePlay"
    >
      <img src="../../chat-v3/icons/voice-play.svg" alt="Voice Play" width="16" height="12"/>
      <span>{{ isVoicePlaying ? '停止播放' : '语音播放' }}</span>
    </div>
    <div
      class="action-button"
      :class="{ active: message.liked }"
      @click="handleLike"
    >
      <img src="../../chat-v3/icons/like.svg" alt="Like" width="14" height="14"/>
      <span>{{ message.liked ? '取消点赞' : '点赞' }}</span>
    </div>
    <div
      class="action-button"
      :class="{ disabled: isDownloading }"
      @click="handleDownload"
    >
      <img src="../../chat-v3/icons/download.svg" alt="Download" width="14" height="14"/>
      <span>{{ isDownloading ? '下载中...' : '下载' }}</span>
    </div>
  </div>
</template>

<script>
import { downloadDocByHtml } from '@/api/security/riskAnalysis/normal'
import MarkdownIt from 'markdown-it'

// 语音合成相关
const synth = window.speechSynthesis
const speech = new SpeechSynthesisUtterance()

// markdown-it 实例
const md = new MarkdownIt()

export default {
  name: 'MessageToolbar',
  props: {
    // 消息对象
    message: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      isVoicePlaying: false,
      isDownloading: false
    }
  },

  mounted() {
    window.addEventListener('beforeunload', () => {
      synth.cancel()
    })
  },
  beforeDestroy() {
    synth.cancel()
  },
  methods: {

    extractPlainText(text) {
      if (!text) return ''
      if (!text.includes('<')) {
        return text.trim()
      }
      return text.replace(/<[^>]*>/g, '').trim()
    },

    handleCopy() {
      if (!this.message.answer) {
        this.$message?.error('没有可复制的内容')
        return
      }

      // 移除think标签后复制
      const contentToCopy = this.getContentWithoutThink(this.message.answer)
      if (!contentToCopy.trim()) {
        this.$message?.error('没有可复制的内容')
        return
      }

      this.$copyText(contentToCopy)
    },

    // 移除think标签的内容
    getContentWithoutThink(text) {
      if (!text) return ''

      let content = text

      // 移除完整的think标签
      content = content.replace(/<think>[\s\S]*?<\/think>/g, '')

      // 移除未闭合的think标签
      content = content.replace(/<think>[\s\S]*$/, '')

      return content.trim()
    },

    handleDelete() {
      this.$emit('delete', this.message.id)
    },



    handleVoicePlay() {
      synth.cancel()

      if (this.isVoicePlaying) {
        synth.cancel()
        this.isVoicePlaying = false
      } else {
        const plainText = this.extractPlainText(this.message.answer)
        if (!plainText) {
          this.$message?.warning('没有可播放的文本内容')
          return
        }

        speech.text = plainText
        speech.lang = 'zh-CN'
        speech.volume = 1
        speech.rate = 1
        speech.pitch = 1

        speech.onend = () => {
          this.isVoicePlaying = false
          this.$forceUpdate()
        }

        speech.onerror = (event) => {
          console.error('语音播放错误:', event)
          this.isVoicePlaying = false

          if (event.error && event.error !== 'interrupted' && event.error !== 'canceled') {
            this.$message?.error('语音播放失败')
          }
        }

        synth.speak(speech)
        this.isVoicePlaying = true
      }
    },

    handleLike() {
      this.$emit('like', {
        message: this.message,
        liked: !this.message.liked
      })
      this.$forceUpdate()
    },

    handleDownload() {
      if (this.isDownloading) return

      if (!this.message.answer) {
        this.$message?.error('没有可下载的内容')
        return
      }

      // 移除think标签后获取内容
      const contentToDownload = this.getContentWithoutThink(this.message.answer)
      if (!contentToDownload.trim()) {
        this.$message?.error('没有可下载的内容')
        return
      }

      this.isDownloading = true

      // 使用markdown-it转为HTML
      const htmlStr = md.render(contentToDownload)

      downloadDocByHtml({
        htmlStr,
      }).then((res) => {
        try {
          let blob = new Blob([res])
          blob.text().then((result) => {
            try {
              let res = JSON.parse(result)
              if (res.code === 500) {
                this.$message.error(res.msg)
                return
              }
            } catch (error) {
              let objectUrl = URL.createObjectURL(blob)
              let link = document.createElement("a")
              // 生成文件下载
              const timestamp = new Date().getTime()
              link.download = `download_${timestamp}.docx`
              link.href = objectUrl
              link.click()
              link.remove()
            }
          }).finally(() => {
            this.isDownloading = false
          })
        } catch (error) {
          console.log(error)
          this.$message.error('下载失败')
          this.isDownloading = false
        }
      }).catch((error) => {
        console.log(error)
        this.$message.error('下载失败')
        this.isDownloading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>


.message-actions {
  display: flex;
  gap: 4px;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #E8E8E8;
  margin-top: 12px;

  .action-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    color: #313233;
    background: transparent;
    border: none;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #F5F6F7;
    }

    &.active {
      color: #409eff;
      background-color: #f0f9ff;
    }

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
      pointer-events: none;
    }

    svg {
      flex-shrink: 0;
    }

    span {
      white-space: nowrap;
    }
  }
}
</style>
