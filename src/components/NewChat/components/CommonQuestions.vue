<template>
  <div class="common-questions-section">
    <div class="common-questions-title">
      常用问题
      <div class="icon-wrapper" title="设置常用问题" @click="getSetListHelp">
        <i class="el-icon-s-tools"></i>
      </div>
    </div>

    <div class="common-questions-grid">
      <div v-if="loading" class="loading-questions">
        <div class="loading-spinner"></div>
        <span>加载中...</span>
      </div>
      <template v-else-if="list.length > 0">
        <div
          class="question-item"
          v-for="item in list"
          :key="item.id"
          @click="handleClick(item)"
        >
          {{ item.query }}
        </div>
      </template>
      <div v-else class="empty-state">暂无常用问题</div>
    </div>
    <SetHelp ref="SetHelp" @updataHelpList="updataHelpList" />
  </div>
</template>

<script>
import { listHelp, getHelpRank } from "@/api/techdocmanage/help";
import SetHelp from "./SetHelp.vue";

export default {
  name: "CommonQuestions",
  components: { SetHelp },
  props: {
    knowledgeName: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      list: [],
      faq: null,
      loading: false,
    };
  },
  watch: {
    knowledgeName: {
      handler() {
        this.getListHelp();
      },
      immediate: true,
    }
  },
  methods: {
    async getListHelp() {
      if (!this.knowledgeName) return; // Add guard clause
      this.loading = true;
      try {
        const { code, rows } = await listHelp({
          pageNum: 1,
          pageSize: 15,
          knowledgeName: this.knowledgeName,
          faq: this.faq,
        });
        if (code === 200) {
          this.list = rows.map((v) => ({
            id: v.id,
            query: v.faq,
          }));
        }
      } finally {
        this.loading = false;
      }
    },
    getSetListHelp() {
      this.$refs.SetHelp.open(this.knowledgeName);
    },
    updataHelpList() {
      this.getListHelp();
    },
    async handleClick(item) {
      await getHelpRank({ id: item.id });
      this.$emit("getUseFaq", item.query);
    },
    getRandomNum() {
      return Math.floor(Math.random() * 1000000);
    },
  },
};
</script>

<style lang="scss" scoped>
.common-questions-section {
  padding: 0 10px;
  margin-top: 20px;
}

.common-questions-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #606266;
}

.icon-wrapper {
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  &:hover {
    background-color: #f5f5f5;
  }
}

.common-questions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.question-item {
  background-color: #f7f8fb;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
  &:hover {
    background-color: #f0f2f5;
  }
}

.loading-questions,
.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 20px;
  color: #909399;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 5px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
