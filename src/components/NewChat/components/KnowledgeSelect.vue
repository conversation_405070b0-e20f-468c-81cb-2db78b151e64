<template>
  <div class="knowledge-toolbar">
    <div v-if="knowledgeLoading" class="knowledge-loading">
      <div class="loading-spinner"></div>
      <span>加载知识库...</span>
    </div>
    <template v-else>
      <button
        v-for="knowledge in knowledgeList"
        :key="knowledge.label"
        :class="['knowledge-btn', { active: knowledge.label === currentKnowledgeLabel }]"
        @click="handleKnowledgeSelect(knowledge)"
        :title="knowledge.label"
      >
        <img
          :src="getImageUrl(knowledge.img)"
          alt="Knowledge"
          width="16"
          height="16"
          class="knowledge-icon"
        />
        <span class="knowledge-name">{{ knowledge.label }}</span>
      </button>
    </template>
  </div>
</template>

<script>
import { getKnowledgeList, getChatSetting } from "./commonReq.js";
import { setImgUrl } from "@/utils/utili/imgUrl";

export default {
  name: "KnowledgeSelect",
  data() {
    return {
      knowledgeList: [],
      knowledgeLoading: false,
      currentKnowledgeLabel: "",
      urlOrigin: null,
    };
  },
  created() {
    this.getConfigKey("minio").then((response) => {
      this.urlOrigin = response.msg;
    });
    this.getList();
  },
  methods: {
    getImageUrl(imgUrl) {
      return setImgUrl(imgUrl, this.urlOrigin);
    },
    handleKnowledgeSelect(knowledge) {
      if (knowledge.label === this.currentKnowledgeLabel) {
        return;
      }
      this.currentKnowledgeLabel = knowledge.label;
      this.$emit("getknowledgeValue", knowledge.label);
    },
    async getList() {
      this.knowledgeLoading = true;
      try {
        const { userId, nickName } = this.$store.state.user;
        let { knowledgeList, knowledgeValue } = await getKnowledgeList(
          userId,
          nickName,
          this.$store.state.common.chat
        );
        const { code, data } = await getChatSetting();

        knowledgeValue = this.$store.state.common.isEnterpriseKnowledge
          ? this.$store.state.common.chat
          : knowledgeValue;

        if (code === 200) {
          this.knowledgeList = knowledgeList.map((v) => {
            const img = data.find((item) => item.knowledge_name === v.label)?.image || "";
            return {
              ...v,
              img,
            };
          });
        } else {
          this.knowledgeList = knowledgeList;
        }

        if (knowledgeValue) {
            this.currentKnowledgeLabel = knowledgeValue;
            this.$emit("getknowledgeValue", knowledgeValue);
        } else if (this.knowledgeList.length > 0) {
            this.currentKnowledgeLabel = this.knowledgeList[0].label;
            this.$emit("getknowledgeValue", this.knowledgeList[0].label);
        }

      } catch (error) {
        console.error("Failed to load knowledge list:", error);
      } finally {
        this.knowledgeLoading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.knowledge-toolbar {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  max-height: 120px;
  overflow-y: auto;
  padding: 8px 20px; // Adjusted padding
}

.knowledge-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 5px 15px;
  color: #666;
  font-size: 12px;

  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

.knowledge-btn {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 20px;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  min-width: 0;
  flex-shrink: 1;

  &:hover {
    border-color: #2d8cf0;
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(45, 140, 240, 0.1);
  }

  &.active {
    background-color: #e6f3fe;
    border-color: #2d8cf0;
    color: #2d8cf0;
    box-shadow: 0 2px 4px rgba(45, 140, 240, 0.2);
  }

  .knowledge-icon {
    flex-shrink: 0;
    border-radius: 2px;
  }

  .knowledge-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
    min-width: 0;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
