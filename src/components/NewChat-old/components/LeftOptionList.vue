<template>
  <div class="leftMain">
    <header>
      <ul class="flex topUl">
        <li
          v-for="(item, index) in options"
          @click="handleTop(item, index)"
          :class="{ topCureent: index === topCureent }"
          :key="index"
        >
          {{ item.label }}
        </li>
      </ul>
    </header>
    <div class="content">
      <el-link
        style="width: 30%"
        :underline="false"
        v-if="list.length && isShowAllDel"
        @click="handleDel()"
        type="danger"
        >全部删除</el-link
      >
      <el-input
        v-if="isfaq"
        placeholder="请输入常见问题"
        v-model="faq"
        size="small"
        style="width: 100%"
        @keyup.enter.native="getListHelp"
        clearable
        @clear="getListHelp"
      >
        <el-button slot="prepend" icon="el-icon-s-tools" @click="getSetListHelp"
          >设置</el-button
        >
        <el-button
          slot="append"
          icon="el-icon-search"
          @click="getListHelp"
        ></el-button>
      </el-input>

      <div ref="scrollContainer" class="list-content">
        <!-- v-infinite-scroll="load"
          :infinite-scroll-immediate="false"
          :infinite-scroll-disabled="disabled" -->
        <ul v-loading="loading" class="ulScrollbar">
          <li
            v-for="(item, index) in list"
            :key="item.id + getRandomNum()"
            :class="{
              current: index === currentIndex,
              'hover-class': isHovered && index === hoveredIndex,
            }"
            @click="debounceHandle(item, index)"
            @mouseover="handleMouseOver(index)"
            @mouseout="handleMouseOut(index)"
          >
            <span class="text">
              {{ item.query }}
            </span>
            <template v-if="!isfaq && !isOptionsCenter">
              <el-button
                v-show="index === hoveredIndex"
                style="
                  width: 16px;
                  height: 16px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                "
                size="mini"
                type="success"
                icon="el-icon-plus"
                circle
                @click.stop="handleAdd(item.query)"
              ></el-button>
              <el-button
                v-show="index === hoveredIndex"
                style="
                  width: 16px;
                  height: 16px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                "
                size="mini"
                type="danger"
                icon="el-icon-delete"
                circle
                @click.stop="handleDel(item.id)"
              ></el-button>
            </template>
          </li>
          <template v-if="list.length">
            <li v-if="isMore && !noMoreData" class="more">
              <span>加载中...</span>
              <PulseLoader :loading="true" size="8px" />
            </li>
            <li v-if="noMoreData" class="noMoreData">
              <span>没有更多数据了</span>
            </li>
          </template>

          <el-empty v-if="!list.length" description="暂无数据"></el-empty>
        </ul>
      </div>

      <footer style="margin-top: 10px">
        <div v-if="debugMode == '1'" class="mb10">
          <span>向量数配置：</span>
          <el-input-number
            style="width: 65%"
            @change="changeTopK"
            v-model.number="top_k"
            :precision="0"
            label="匹配向量数"
          ></el-input-number>
        </div>
        <!-- <div v-if="currentRoleArr.length"> -->
        <div v-show="false">
          <span>角 色 配 置：</span>
          <el-input
            style="width: 65%; margin-left: 2px"
            placeholder="当前角色"
            v-model="currentRole"
            :disabled="true"
          >
          </el-input>
          <!-- <el-select
            style="width: 65%;margin-left: 2px;"
            v-model="currentRole"
            placeholder="请选择当前角色"
            clearable
            @change="handleRole"
          >
            <el-option
              v-for="item in currentRoleArr"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select> -->
        </div>
      </footer>
    </div>
    <SetHelp ref="SetHelp" @updataHelpList="updataHelpList" />
  </div>
</template>

<script>
import throttle from "lodash/throttle";
import { getHistory } from "./commonReq.js";
import { listHelp, getHelpRank } from "@/api/techdocmanage/help";
import serviceKnowledge from "@/api/knowledge.js";
import { getUser } from "@/api/system/user";
import { parseMarkdown, isHtml } from "../utils";
import SetHelp from "./SetHelp.vue";
import { addHelp } from "@/api/techdocmanage/help";
import _debounce from "lodash/debounce";
import PulseLoader from "vue-spinner/src/PulseLoader.vue";
export default {
  name: "LeftOptionList",
  components: { SetHelp, PulseLoader },
  props: {},
  data() {
    return {
      options: [
        {
          label: "对话列表",
          value: 0,
        },
        //         {
        //   label: "功能中心",
        //   value: 1,
        // },
        {
          label: "常用问题",
          value: 2,
        },
      ],
      // 标题选中
      topCureent: 0,
      // 知识库名
      knowledgeName: null,
      // 问答历史列表
      list: [],
      currentIndex: null,
      hoveredIndex: null, // 鼠标悬停项的索引
      isHovered: false, // 是否有元素被鼠标悬停
      // 常见问题
      faq: null,
      // 是否常见问题
      isfaq: false,
      // 聊天配置
      chatSet: null,
      // 当前AI配置角色数组
      currentRoleArr: [],
      // 角色选中值
      currentRole: null,
      // 是否功能中心
      isOptionsCenter: false,
      loading: false,
      // 是否调试
      debugMode: "",
      // 匹配向量数
      top_k: 0,
      // 是否显示全部删除操作
      isShowAllDel: false,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      //是否加载更多
      isMore: false,
      noMoreData: false,
      infiniteDisabled: false,
      total: 0,
    };
  },
  computed: {},
  watch: {
    "$store.state.common.files.length": {
      handler(val) {
        console.log(val);
        if (val === 1) {
          this.options = [
            {
              label: "对话列表",
              value: 0,
            },
            // {
            //   label: "功能中心",
            //   value: 1,
            // },
            // {
            //   label: "常用问题",
            //   value: 2,
            // },
          ];
        } else if (val >= 2) {
          this.options = [
            {
              label: "对话列表",
              value: 0,
            },
            // {
            //   label: "功能中心",
            //   value: 1,
            // },
            // {
            //   label: "常用问题",
            //   value: 2,
            // },
          ];
        } else {
          this.options = [
            {
              label: "对话列表",
              value: 0,
            },
            // {
            //   label: "功能中心",
            //   value: 1,
            // },
            {
              label: "常用问题",
              value: 2,
            },
          ];
        }
      },
      immediate: true,
    },
  },
  created() {
    this.getConfigKey("debugMode").then((response) => {
      this.debugMode = response.msg;
    });
  },
  mounted() {},
  beforeDestroy() {
    // 移除滚动事件监听
    this.$refs.scrollContainer.removeEventListener("scroll", this.handleScroll);
  },
  methods: {
    initData(val) {
      this.$nextTick(() => {
        this.chatSet = val;
        this.top_k = val.top_k;
        this.knowledgeName = val.knowledge_name;
        this.topCureent = 0;
        this.currentIndex = null;
        this.isfaq = false;
        this.isOptionsCenter = false;
        this.isMore = false;
        this.noMoreData = false;
        this.isShowAllDel = true;
        this.queryParams.pageNum = 1;
        this.getList();
        this.getUserAIRoleAndModel();
        this.$forceUpdate();
        this.$refs.scrollContainer.scrollTop = 0;
      });
    },
    // 标题点击切换
    handleTop(item, index) {
      console.log(item.label);
      this.topCureent = index;
      this.currentIndex = null;
      this.queryParams.pageNum = 1;
      this.list = [];
      if (item.label === "常用问题") {
        this.getListHelp();

        this.isfaq = true;
        this.isOptionsCenter = false;
        this.isShowAllDel = false;
      } else if (item.label === "功能中心") {
        this.getOptionsList();
        this.isfaq = false;
        this.isOptionsCenter = true;
        this.isShowAllDel = false;
      } else {
        this.isfaq = false;
        this.isOptionsCenter = false;
        this.isShowAllDel = true;
        this.getList();
      }
    },
    // 获取问答列表
    async getList() {
      this.loading = true;
      // const { code, data } = await getHistory(this.knowledgeName);
      const { code, rows, total } = await serviceKnowledge.getHistoryList({
        knowledge_name: this.knowledgeName,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        generate: "0",
      });
      if (code === 200) {
        // this.list = data;
        this.list = rows;
        this.total = total;
        this.loading = false;
        // this.infiniteDisabled = false
        // this.isMore = true;
        // 监听滚动事件并加上节流（500ms 内只能执行一次）
        this.$refs.scrollContainer.addEventListener(
          "scroll",
          throttle(this.handleScroll, 500) // 每 500ms 触发一次
        );
      }
    },
    // 生成随机数
    getRandomNum() {
      return Math.floor(Math.random() * 1000000);
    },
    // 监听滚动事件
    handleScroll() {
      const container = this.$refs.scrollContainer;
      const scrollTop = container.scrollTop;
      const clientHeight = container.clientHeight;
      const scrollHeight = container.scrollHeight;
      console.log(scrollTop + clientHeight >= scrollHeight);
      // 判断是否滚动到底部
      if (scrollTop + clientHeight >= scrollHeight) {
        this.isMore = true;
        this.loadMore();
      }
    },
    // 加载更多数据
    async loadMore() {
      if (this.noMoreData) return;

      // 模拟获取新数据
      const { code, rows, total } = await serviceKnowledge.getHistoryList({
        knowledge_name: this.knowledgeName,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        generate: "0",
      });
      if (code === 200) {
        if (this.list.length * 1 >= total) {
          this.isMore = false;
          this.noMoreData = true;
          console.log("没有更多数据", this.isMore);
          return;
        } else {
          // 非常用问题状态下更新列表数据
          if (!this.isfaq) {
                      this.list = [...this.list, ...rows];
              this.isMore = false;
              this.queryParams.pageNum++;
              console.log("触底加载", this.queryParams.pageNum);
          }else{
             this.isMore = false;
          }

        }
      }
    },
    // 获取常见问题列表
    async getListHelp() {
      this.loading = true;
      const { code, rows } = await listHelp({
        pageNum: 1,
        pageSize: 15,
        knowledgeName: this.knowledgeName,
        faq: this.faq,
      });
      if (code === 200) {
        this.list = rows.map((v) => {
          return {
            value: v.id,
            id: v.id,
            query: v.faq,
          };
        });
        this.loading = false;
      }
    },
    // 获取功能中心列表
    async getOptionsList() {
      this.loading = true;
      const { code, data } = await serviceKnowledge.getKnowledgeSetting();
      if (code === 200) {
        this.list = data
          .filter((v) => v.type === "1")
          .map((v, index) => {
            return {
              label: v.knowledge_name,
              query: v.knowledge_name,
              value: index,
              remark: v.remark,
              ...v,
            };
          });
        this.loading = false;
      }
    },
    // 常见问题列表设置
    getSetListHelp() {
      this.$refs.SetHelp.open(this.knowledgeName);
    },
    // 更新常见问题列表数据
    updataHelpList() {
      this.getListHelp();
    },
    debounceHandle(item, index) {
      this.currentIndex = index;
      this._debounceHandleClick(item, index);
    },
    _debounceHandleClick: _debounce(async function (item, index) {
      try {
        console.log(item);
              const { code, data } = await serviceKnowledge.getHistoryChat(item.id);
              if (code===200) {
                await this.handleClick(Object.assign(item,data), index);
              }

      } catch (error) {
        console.error("handleClick failed:", error);
      }
    }, 500),
    // 列表问题事件
    async handleClick(item, index) {
      if (this.isfaq) {
        await getHelpRank({ id: item.id });
        this.$emit("getUseFaq", item.query);
      } else {
        if (this.isOptionsCenter) {
          this.$emit("getOptions", { query: item.remark, ...item });
        } else {
          // let str = item.format_result ? item.format_result : item.result;
          console.log(item.result);
          let str = "";
          console.log(isHtml(item.result));
          if (isHtml(item.result)) {
            str = item.result;
          } else {
            str = item.format_result ? item.result : parseMarkdown(item.result);
          }

          this.$emit("getHistory", {
            id: item.id,
            answer: str,
            done: true,
            isStop: true,
            query: item.query,
            good: item.chat_like === "1" ? true : false,
            tableStr: item.format_result,
          });
          console.log("问答历史", {
            query: item.query,
            answer: str,
            good: item.chat_like === "1" ? true : false,
            bad: item.bad,
            id: item.id,
            tableStr: item.format_result,
          });
        }
      }
    },
    handleMouseOver(index) {
      this.hoveredIndex = index;
      this.isHovered = true;
    },
    handleMouseOut(index) {
      if (this.hoveredIndex === index) {
        this.hoveredIndex = null;
        this.isHovered = false;
      }
    },
    // 数据删除
    async handleDel(id) {
      const ids = id || this.list.map((v) => v.id);
      const { code, data } = await serviceKnowledge.getHistoryDeleted(ids);
      if (code === 200) {
        this.getList();
      }
    },
    // 添加到常用问题
    handleAdd(query) {
      addHelp({
        knowledgeName: this.knowledgeName,
        faq: query,
      }).then((response) => {
        this.msgSuccess("常用问题新增成功");
      });
    },
    handleRole() {
      this.$emit("getRole", { query: this.currentRole });
    },
    changeTopK() {
      this.$emit("updataTopK", { top_k: this.top_k });
    },
    async getUserAIRoleAndModel() {
      // 模型列表
      //       const resModel= await serviceKnowledge.getModelList({
      //   pageNum: 1,
      //   pageSize: 9999,
      // });
      // 角色列表
      const { code, rows } = await serviceKnowledge.getPromptList({
        pageNum: 1,
        pageSize: 9999,
        promptType: "knowledge_base_chat",
      });
      if (code === 200) {
        console.log(
          rows.find((i) => i.promptName === this.chatSet?.prompt_name)
        );
        this.currentRole = rows.find(
          (i) => i.promptName === this.chatSet?.prompt_name
        ).promptDescription;

        // 获取登录人配置角色id
        // const res = await getUser(this.$store.state.user.userId);
        // if (res.code === 200) {
        //   this.currentRoleArr = res.promptIds
        //     .map((v) => {
        //       const matchingRow = rows.find((i) => i.id === v);
        //       if (matchingRow) {
        //         return {
        //           id: v,
        //           label: matchingRow.promptDescription,
        //           value: matchingRow.promptName,
        //         };
        //       }
        //     })
        //     .filter((obj) => obj !== null); // 使用 filter 去除 null 值（如果有的话）

        //   this.currentRole =
        //     this.currentRoleArr.find(
        //       (v) => v.value === this.chatSet?.prompt_name
        //     )?.value || this.currentRoleArr[0]?.value;
        // }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.infinite-list {
  height: 300px;
}
.leftMain {
  flex: 0.25;
  padding: 10px;
  border-right: 1px solid #ccc;
  header {
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    .topUl {
      margin: 0;
      justify-content: space-around;
    }
    .topCureent {
      color: #409eff;
      border-bottom: 5px solid #ccc;
      padding-bottom: 5px;
    }
  }
  .content {
    height: 93%;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;
    margin: 10px 5px;
    overflow: hidden;
    width: calc(100vw - 1580px);
    min-width: 335px;
    li {
      width: 100%; /* 设置容器宽度 */
      min-width: 335px;
      font-size: 14px;
      line-height: 14px;
      cursor: pointer;
      margin-bottom: 10px;
      height: 26px;
      .text {
        // width: 260px; /* 设置容器宽度 */
        width: calc(100vw - 1650px);
        white-space: nowrap; /* 防止文本换行 */
        overflow: hidden; /* 文本溢出容器时隐藏 */
        text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
        min-width: 270px;
      }

      padding: 5px 10px;
      display: flex;
      justify-content: space-between;
    }
    .current {
      background: linear-gradient(270deg, #c9ecff 0%, #468ff8 100%);
      border-radius: 13px;
      color: aliceblue;
    }
    .hover-class {
      /* 鼠标悬停时的样式 */
      background: linear-gradient(270deg, #c9ecff 0%, #468ff8 100%);
      border-radius: 13px;
      color: aliceblue;
      span {
        line-height: 16px;
        font-size: 16px;
      }
    }
    .listCureent {
      background: linear-gradient(270deg, #c9ecff 0%, #468ff8 100%);
      border-radius: 13px;
      color: aliceblue;
    }
  }
}

::v-deep .el-input--medium .el-input__inner {
  height: 38px !important;
}
::v-deep ul {
  padding-left: 0px !important;
  margin: 10px 0 10px 0px !important;
}
::v-deep .redicon {
  color: "#ff4949" !important;
}
.list-content {
  height: calc(100vh - 213px);
  overflow: auto;
  overflow-x: hidden;
  .more {
    display: flex;
    justify-content: center;
  }
  .noMoreData {
    span {
      text-align: center;
      width: 100%;
    }
  }
}

// 滚动条整体 整体如果不设置，只设置滑块和轨道是没有效果的
.list-content::-webkit-scrollbar {
  width: 5px;
  height: 5px; // height对于纵向滚动条来说没有用，但是对于横向就有用了
  border-radius: 10px;
}
// 滑块
.list-content::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #d3dbe9;
  // height: 50px; // 整体设了宽高后，这里宽高是无效的
  // width: 50px;
}
// 轨道
.list-content::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #d9e3f4;
}
</style>
