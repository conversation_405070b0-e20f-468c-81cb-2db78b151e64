import serviceKnowledge from "@/api/knowledge.js";
import { getUser } from "@/api/system/user";
// import store from "@/store";
import userService from "@/api/techdocmanage/docCenter/user";
import { fetchEventSource } from "@microsoft/fetch-event-source";
// const { userId, nickName } = store.state.user;
// 获取知识库列表
export const getKnowledgeList = async (userId, nickName, chatvalue) => {
  const { code, data } = await serviceKnowledge.getKnowledgeList();
  // 查询所有公开知识库列表
  const openRes = await serviceKnowledge.getKnowledgeOpen();
  //
  if (code === 200) {
    const res = await getUser(userId);
    // let newData = userId === "1" ? data : res.modelIds;
    let newData = res.modelIds;

    // const knowledgeList = newData.map(v=> {
    //   let obj = openRes.data.find((item) => item === v)
    //   if (obj) return obj
    // }).filter(item=>item)
    //   .map((v, index) => {
    const knowledgeList = newData
      .map((v, index) => {
        return {
          label: v,
          value: index,
        };
      })
      .filter((item) => {
        return !(
          (item.label.startsWith("个人文档-") &&
            item.label !== `个人文档-${nickName || ""}`) ||
          item.label.startsWith("template-")
        );
      });

    const value = chatvalue;
    const knowledgeValue = value ? value : knowledgeList[0].label;
    return { knowledgeList, knowledgeValue };
  }
};
// 获取知识库对话历史记录
export const getHistory = async (knowledgeName) => {
  return await serviceKnowledge.getHistory({
    knowledgeName,
  });
};

// 获取聊天系统设置
export const getChatSetting = async (knowledgeName) => {
  return await serviceKnowledge.getChatSetting({
    knowledgeName,
  });
};

// 发送提问请求
export const sendQuestion = async (query, chatSet, history) => {
    const {
      model_name,
      prompt_name,
      score_threshold,
      top_k,
      knowledge_name,
      temperature,
      mix_type
    } = chatSet;
    console.log(chatSet);
    const res = await serviceKnowledge.newChatWithKnowledge(
      JSON.stringify({
        query,
        knowledge_base_name: knowledge_name,
        history: [],
        // history,
        top_k,
        score_threshold,
        stream: false,
        model_name,
        // model_name: modelName,
        temperature,
        max_tokens: 0,
        // prompt_name:prompt_name|| currentRole
        prompt_name,
        type: chatSet?.label || "",
        mix_type
      })
    );
    return res;
};
// 文件预览--打开新窗口
export const previewFileFn = (path, initCurrentIP, userId) => {
  const id = path.substring(path.lastIndexOf("/") + 1);
  const previewPath =
    initCurrentIP + "/filecore/openFile?id=" + id + "&userId=" + userId;
  window.open(previewPath);
};
// 文件下载
export const donwloadFileFn = async (path, userId) => {
  const id = path.substring(path.lastIndexOf("/") + 1);
  let params = {
    id,
    userId,
  };
  const { code, data } = await userService.queryFileDetail(id);
  if (code === 200) {
    userService.downloadSourceFile(params).then((res) => {
      let blob = new Blob([res]);
      blob.text().then((result) => {
        try {
          let res = JSON.parse(result);
          if (res.code === 500) {
            $message.error(res.msg);
            return;
          }
        } catch (error) {
          let objectUrl = URL.createObjectURL(blob);
          let link = document.createElement("a");
          // 源文件下载
          link.download = data.docName;
          // }
          link.href = objectUrl;
          link.click();
          link.remove();
        }
      });
    });
  }
};
const eventSourceAPI = async (
  knowledge_base_name,
  file_name,
  url,
  model_name
) => {
  try {
    let msg = "";
    const ctrl = new AbortController();
    fetchEventSource(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      //请求体，用于给后台的数据
      body: JSON.stringify({
        knowledge_base_name,
        file_name,
        allow_empty_kb: true,
        vs_type: "faiss",
        embed_model: "bge-large-zh-v1.5",
        file_description: "",
        model_name,
        temperature: 0.7,
        stream: true,
      }),
      openWhenHidden: true, // 取消visibilityChange事件
      signal: ctrl.signal,
      onmessage(event) {
        //服务返回的数据
        msg += JSON.parse(event.data).text;
        // that.tslText = msg;
        return event;
      },
      onerror(event) {
        // 服务异常
        console.log("服务异常", event);
        try {
          // onerror后关闭请求，但打包是ts语法报错
          // ctrl.signal.aborted = false;
          if (ctrl) {
            ctrl.abort();
          }
        } finally {
          console.log("finally", ctrl);
        }
      },
      onclose() {
        // 服务关闭
        console.log("服务关闭");
      },
    });
  } catch (error) {
    console.error("Stream fetch error:", error);
    // 这里可以添加用户友好的错误处理
  }
};
// 大文件请求处理
export const sendBigFileQuestion = async (
  knowledge_base_name,
  file_name,
  model_name,
  temperature
) => {
  const url =
    process.env.VUE_APP_BASE_AI +
    "/knowledge_base/kb_summary_api/summary_file_to_vector_store";
  // const res = await eventSourceAPI(knowledge_base_name,file_name,url,model_name)
  const res = await serviceKnowledge.sendBigFileQuestion(
    JSON.stringify({
      knowledge_base_name,
      file_name,
      allow_empty_kb: true,
      vs_type: "faiss",
      embed_model: "bge-large-zh-v1.5",
      file_description: "",
      model_name,
      temperature,
    })
  );
  // return res
  console.log(res);
  let startIndex = res.indexOf("data:") + "data:".length;
  let jsonString = res.substring(startIndex).trim(); // 移除可能的前后空格;
  let newRes = JSON.parse(jsonString);
  return newRes;
};
// sql请求数据转换
const transformArray = (data) => {
  // 使用 Set 来收集所有唯一的键
  let keys = new Set();
  data.forEach((item) => {
    for (let key in item) {
      if (item.hasOwnProperty(key)) {
        // 确保键是对象自身的属性，而不是继承的
        keys.add(key);
      }
    }
  });

  // 将 Set 转换为数组，这将作为表头
  let headers = Array.from(keys);

  // 初始化 newData，首先添加表头
  let newData = [headers];

  // 遍历 arr，并将每个对象转换为与表头对齐的数组
  data.forEach((item) => {
    let row = [];
    headers.forEach((header) => {
      // 如果对象中存在该键，则添加其值；否则添加 null 或其他默认值
      row.push(item[header] !== undefined ? item[header] : null);
    });
    newData.push(row);
  });
  return newData;
};
// 调用数据库问答方法
export const databaseSendQuestion = async (prompt, knowledge_name) => {
  let data = null;
  try {
    const res = await serviceKnowledge.createSQL(prompt);
    if (res.text) {
      // 执行sql
      let executeData = await serviceKnowledge.executeSQL(res.id);
      if (executeData.type === "sql_error") {
        data = {
          text: res.text,
          isflag: false,
        };
        return data;
      } else {
        // 插入sql历史记录
        await serviceKnowledge.saveDatabase({
          knowledge_name,
          query: prompt,
          result: res.text,
        });
        if (executeData.should_generate_chart) {
          let usePlotlyDrawData = await serviceKnowledge.usePlotlyDraw(
            executeData.id
          );
          executeData?.df &&
            (executeData.df = transformArray(JSON.parse(executeData.df)));
          usePlotlyDrawData?.fig &&
            (usePlotlyDrawData.fig = JSON.parse(usePlotlyDrawData.fig));
          data = {
            ...res,
            ...executeData,
            ...usePlotlyDrawData,
            isflag: true,
          };
        } else {
          executeData?.df &&
            (executeData.df = transformArray(JSON.parse(executeData.df)));
          data = {
            ...res,
            ...executeData,
            isflag: true,
          };
        }
      }
    }
    return data;
  } catch (error) {
    console.log(error);
    return false;
  }
};
// 通用数据库回答方法

export const commonDatabaseSendQuestion = async (
  prompt,
  sqlStr,
  knowledge_name
) => {
  let data = null;
  try {
    const res = await serviceKnowledge.generateCreateSQL(prompt, sqlStr);
    if (res.text) {
      // 执行sql
      let executeData = await serviceKnowledge.executeSQL(res.id);
      if (executeData.type === "sql_error") {
        data = {
          text: res.text,
          isflag: false,
        };
        return data;
      } else {
        // 插入sql历史记录
        // await serviceKnowledge.saveDatabase({
        //   knowledge_name,
        //   query: prompt,
        //   result: res.text,
        // });
        if (executeData.should_generate_chart) {
          let usePlotlyDrawData = await serviceKnowledge.usePlotlyDraw(
            executeData.id
          );
          executeData?.df &&
            (executeData.df = transformArray(JSON.parse(executeData.df)));
          usePlotlyDrawData?.fig &&
            (usePlotlyDrawData.fig = JSON.parse(usePlotlyDrawData.fig));
          data = {
            ...res,
            ...executeData,
            ...usePlotlyDrawData,
            isflag: true,
          };
        } else {
          executeData?.df &&
            (executeData.df = transformArray(JSON.parse(executeData.df)));
          data = {
            ...res,
            ...executeData,
            isflag: true,
          };
        }
      }
    }
    return data;
  } catch (error) {
    console.log(error);
    return false;
  }
};
