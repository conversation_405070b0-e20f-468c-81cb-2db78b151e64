<template>
  <div class="knowledgeMain" id="knowledgeMain" >
    <el-carousel
      ref="carouse"
      arrow="always"
      :autoplay="false"
      type="card"
      height="60px"
      @change="getActiveItem"
    >
      <!-- knowledgeList -->
      <CustomSwiper v-for="(item, index) in knowledgeList" :key="index">
        <div
          class="customcarousel"
          :class="{ selectcarousel: index === currentIndex }"
        >
          <img style="width: 24px;" :src="getImageUrl(item.img)||img" alt="" />
          <span>
            {{ item.label }}
          </span>
        </div>
      </CustomSwiper>
      <!-- <CustomSwiper v-for="(item, index) in 9" :key="index">
        <div
          class="customcarousel"
          :class="{ selectcarousel: index === currentIndex }"
        >
          <span>
            {{ item }}
          </span>
        </div>
      </CustomSwiper> -->
    </el-carousel>
  </div>
</template>

<script>
import { getKnowledgeList,getChatSetting } from "./commonReq.js";
import CustomSwiper from "@/components/CustomSwiper";
import {
	setImgUrl
} from "@/utils/utili/imgUrl"
export default {
  name: "",
  components: { CustomSwiper },
  props: {},
  data() {
    return {
      currentIndex: 0,
      //   知识库列表
      knowledgeList: [],
      img:require("@/assets/docCenter/个人文档-超级管理员.png"),
	  urlOrigin:null
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getList();
	this.getConfigKey("minio").then((response) => {
	  this.urlOrigin=response.msg;
	});
  },
  mounted() {},
  methods: {
	getImageUrl(imgUrl) {
	  	return setImgUrl(imgUrl,this.urlOrigin);
	},
    getActiveItem(val) {
      this.currentIndex = val;
      // 选中值
      const knowledgeValue = this.knowledgeList.find(
        (v, index) => index === val
      ).label;
      this.$emit("getknowledgeValue", knowledgeValue);
    },
    // 获取知识库列表
    async getList() {
      const {userId,nickName} =  this.$store.state.user;
      let { knowledgeList, knowledgeValue } = await getKnowledgeList(userId,nickName,this.$store.state.common.chat);
      const {code,data} = await getChatSetting()
    knowledgeValue=  this.$store.state.common.isEnterpriseKnowledge?this.$store.state.common.chat:knowledgeValue
      const currentIndex = knowledgeList.findIndex(
        (v) => v.label === knowledgeValue
      );
      if (code ===200) {
              this.knowledgeList= knowledgeList.map(v=>{
        const img= data.find(item=>item.knowledge_name===v.label)?.image||''
        return {
          ...v,
          img
        }
      })
      }

      // this.knowledgeList = knowledgeList;
      this.$nextTick(() => {
        currentIndex !== -1 && this.$refs.carouse.setActiveItem(currentIndex);
      });
      this.$emit("getknowledgeValue", knowledgeValue);
    },
  },
};
</script>

<style scoped lang="scss">
.knowledgeMain {
  width: 100%;
  height: 70px;
  padding: 10px;
  border-bottom: 1px solid #ccc;
}
::v-deep .el-carousel__indicators--outside {
  display: none !important;
}
::v-deep .el-carousel__mask {
  display: none !important;
}
.customcarousel {
  width: 100%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    margin: 0 5px;
  }
}

.selectcarousel {
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  border-radius: 1rem;
  background-color: #b5beedf0;
}
.el-carousel__item {
  display: flex;
  justify-content: center;
}
</style>
