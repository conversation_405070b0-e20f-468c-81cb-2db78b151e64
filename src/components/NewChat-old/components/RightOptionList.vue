<template>
	<div class="rightMain">
		<div class="right-title">
			AI工具集
		</div>
		<div class="tool-list" v-for="(item,index) in toolTypeOptions" :key="index">
			<div class="tool-classify-title">
			{{item.dictLabel}}
			</div>
			<ul>
				<li v-for="(itemTool,indexTool) in toolClassify(item.dictValue)" @click="toolChange(itemTool)">
					<img :src="itemTool.image" alt="" />{{itemTool.toolName}}
				</li>
			</ul>
		</div>
	</div>
</template>

<script>
	import {
		listAllKnowledgeAiTool,
	} from "@/api/techdocmanage/knowledgeAiTool";
	export default {
		data() {
			return {
				toolList: [],
				toolTypeOptions: []
			}
		},
		computed: {
			toolClassify() {
				return (value) => {
					return this.toolList.filter(item=>item.toolType==value)
				}
			}
		},
		created() {
			this.getDicts("doc_knowledge_aitool_type").then(response => {
				this.toolTypeOptions = response.data;
				this.getList()
			});

		},
		methods: {
			getList() {
				listAllKnowledgeAiTool().then(response => {
					this.toolList = response.data;
				});
			},
			toolChange(itemTool){
				window.open(itemTool.url, '_blank');
			}
		}
	}
</script>

<style lang="scss" scoped>
	.rightMain{
		width: 240px;
		padding: 0 20px;
		border-left:1px solid #ccc;
		height: 100%;
		overflow-y: auto;
		.right-title{
			font-size: 18px;
			height: 50px;
			line-height: 50px;
		}
		.tool-classify-title{
			height: 40px;
			line-height: 40px;
			color: #757575;
		}
		li{
			height: 40px;
			padding-left: 15px;
			cursor: pointer;
			display: flex;
			align-items: center;
			img{
				width: 32px;
				height: 32px;
				display: inline-block;
				margin-right: 15px;
			}
		}
	}
</style>