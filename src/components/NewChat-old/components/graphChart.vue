<template>
  <el-row class="MainArea">
    <el-col :span="24" class="Mainleft" v-loading="fullscreenLoading">
      <div class="left" id="viz1" ref="viz1"></div>
    </el-col>
  </el-row>
</template>
<script>
import NeoVis from "neovis.js/dist/neovis.js";

export default {
  props: {
    // SQL: {
    //   typeof: String,
    //   default: "",
    // },
  },
  data() {
    return {
      viz: {},
      // SQL: "MATCH (e:element_0) WHERE e.ELEMENT_NAME CONTAINS '计划' RETURN e", //写自己的Cypher
      // SQL: "MATCH (n) WHERE EXISTS(n.ELEMENT_NAME) RETURN DISTINCT "node" as entity, n.ELEMENT_NAME AS ELEMENT_NAME LIMIT 25 UNION ALL MATCH ()-[r]-() WHERE EXISTS(r.ELEMENT_NAME) RETURN DISTINCT "relationship" AS entity, r.ELEMENT_NAME AS ELEMENT_NAME LIMIT 25", //写自己的Cypher
      // SQL:"MATCH (a:element_0 )-[r:rel|asn|quote*0..1]-(c:element_0 ) where a.ELEMENT_NAME CONTAINS '计划' return a,r,c LIMIT 50",
      // SQL:"MATCH (e:element_0)   WHERE e.ELEMENT_NAME CONTAINS '计划' RETURN e.ELEMENT_NAME, e.MODEL_STATE, e.MODELTABLE_ID, e.MODEL_TYPE_ID, e.SECURITY_CLASS, e.DATA_BANK_ID, e.SY_NAME, e.TYPE_ID, e.MODEL_ID, e.ELEMENT_ID, e.ELEMENT_TYPE, e.ELEMENT_SOURCE LIMIT 10",
      // SQL:"MATCH (e:element_0) WHERE e.ELEMENT_NAME CONTAINS '计划' OR e.SY_NAME CONTAINS '计划' RETURN e AS node, [(e)-[r]->()|r] AS relationships UNION MATCH (m:model)WHERE m.MODEL_TYPE_ID CONTAINS '计划'RETURN m AS node, [(m)-[r]->()|r] AS relationships",
      SQL: "MATCH (e:element_0)-[r]-(m) WHERE e.ELEMENT_NAME CONTAINS '潜在风险' OR r.SIGN CONTAINS '潜在风险' RETURN e, r, m LIMIT 100",

      driver: null,
      cypherkeyword: false,
      graphtable: false,
      records: [],
      clearAll: false,

      currentGraph: {
        nodes: {},
        links: {},
      },
      nodeMap: {},
      network: "",

      fullscreenLoading: false,
      vizObj: "",
    };
  },
  created() {},

  mounted() {
    this.draw();
  },
  watch: {
    SQL: {
      handler(newData) {
        this.$nextTick(() => {
          setTimeout(() => {
            // this.draw();
          }, 1000);
          // this.executeCypher()
        });
      },
      //   immediate: true,
      //   deep: true,
    },
  },
  methods: {
    draw(sql) {
      this.SQL = sql;
      //    this.canvas = document.getElementById("js-canvas");
      let viz1 = this.$refs.viz1;
      let viz;

      let config = {
        container_id: "viz1",
        server_url: "bolt://192.168.0.150:7688", //一般是这个
        server_user: "neo4j", //默认neo4j
        server_password: "a1b2c3d4", //写自己的密码

        // /labels是节点央样式的配置：
        /// caption ：节点显示的文字对应内容
        /// community： 节点颜色
        /// size：节点大小
        /// font：节点字体大小设置
        //没有在这个地方配置的节点将会是默认样式

        labels: {
          //   "名称": {caption: "properties", community: "#b8ff54", size: 200, font: {size: 12, color: "#d9960e",},}, //根据自己的来
          //   "属性": {caption: "DATA_BANK_ID", community: "#c61625", size: 200, font: {size: 12, color: "#901540",},},  //根据自己的来
          element_0: {
            caption: "ELEMENT_NAME",
            community: "#000",
            size: 20,
            font: { size: 12, color: "#901540" },
          }, //根据自己的来
          sysDocument: {
            caption: "doc_name",
            community: "#000",
            size: 20,
            font: { size: 12, color: "#901540" },
            // "image": 'https://visjs.org/images/visjs_logo.png',
          }, //根据自己的来
          lowTerm: {
            // caption: "low_name",
            caption: (val) => {
              let name = val.properties.term_no
                ? "---" + val.properties.term_no
                : "";
              return val.properties.low_name + name;
            },
            // "image": 'https://visjs.org/images/visjs_logo.png',
            community: "#000",
            size: 20,
            font: { size: 12, color: "#901540" },
          },
          Low: {
            caption: "low_name",
            // caption: (val)=>{console.log(val);},
            // "image": 'https://visjs.org/images/visjs_logo.png',
            community: "#000",
            size: 20,
            font: { size: 12, color: "#901540" },
          },
        },
        ///relationships是关系线段样式的配置：
        /// caption ：线段显示的文字对应内容
        /// thickness： 线段粗细
        /// font：节点字体大小设置
        //没有在这个地方配置的线段将会是默认样式

        relationships: {
          ELEMENT_NAME: {
            thickness: 10,
            caption: true,
            font: { size: 15, color: "#CCC" },
          },
        },
        arrows: true,
        // hierarchical: true,

        // 分层结构或者默认 "hubsize"（默认）和"directed".
        // hierarchical_sort_method: 'hubsize',
        // hierarchical_sort_method: 'directed',
        initial_cypher: this.SQL,
        // initial_cypher: ' MATCH (e:element_0)-[r]->(f:element_0)   WHERE e.ELEMENT_NAME CONTAINS "潜在风险" OR f.ELEMENT_NAME CONTAINS "潜在风险"   RETURN e, r, f;',
      };

      viz = new NeoVis(config);
      viz._container = viz1;
      viz.render();
      this.vizObj = viz;
    },

    submit() {
      const cypher = $("#cypher").val();
      if (cypher.length > 3) {
        // this.SQL = cypher;
        // this.viz.renderWithCypher(cypher);
      } else {
        // this.viz.reload();
      }
    },
    stabilize() {
      this.viz.stabilize();
    },
    executeCypher(query) {
      var neo4j = require("neo4j-driver");
      this.echartsNode = []; //节点数组
      this.nodesRelation = []; //关系线数组
      this.category = []; //echarts图例数据数
      // 创建实例
      this.driver = neo4j.driver(
        "bolt://192.168.0.150:7688",
        neo4j.auth.basic("neo4j", "a1b2c3d4")
      );
      console.log(
        "🚀 ~ file: AuthorArticleSearch.vue ~ line 46 ~ mounted ~  this.drive",
        this.driver
      );

      let me = this;
      me.records = [];
      this.clearAll = true;
      let session = this.driver.session();
      if (this.SQL == "") return;
      session
        .run(this.SQL, {})
        .then((result) => {
          me.clearAll = false;
          me.records = result.records;
          console.log("neo4j 查询结果", result.records);
          this.vizObj.renderWithCypher(this.SQL);
          // 开始处理数据
          // for (let i = 0; i < me.records.length; i++) {
          //   this.echartsData.push({
          //     name: me.records[i]._fields[0].segments[0].start.properties.name,
          //     category: me.records[i]._fields[0].segments[0].start.labels[0]
          //   });
          //   this.echartsData.push({
          //     name: me.records[i]._fields[0].segments[0].end.properties.name,
          //     category: me.records[i]._fields[0].segments[0].end.labels[0]
          //   });
          //   this.nodesRelation.push({
          //     source: me.records[i]._fields[0].segments[0].start.properties.name,
          //     target: me.records[i]._fields[0].segments[0].end.properties.name,
          //     name: me.records[i]._fields[0].segments[0].relationship.type,
          //   });
          // }

          //删除arr中的重复对象
          // var arrId = [];
          // var legend = [];
          // for (var item of this.echartsData) {
          //   legend.push({ name: item.category })
          //   if (arrId.indexOf(item.name) == -1) {
          //     arrId.push(item.name)
          //     this.echartsNode.push(item);
          //   }
          // }
          // this.category = Array.from(new Set(legend))

          session.close();
          me.closeLoading(false);
        })
        .catch(function (error) {
          console.log("Cypher 执行失败！", error);
          me.driver.close();
        });

      setTimeout(() => {
        this.knowlegGraphshow = true;
      }, 4000);
    },
    closeLoading(status) {},
  },
};
</script>
<style scoped>
.LayOutBody {
  width: 100%;
  height: 100%;
  border: 10px solid #eaecef;
}

/* 头部搜索条件 */
.SearchHeader {
  height: 82px;
  border-bottom: 8px solid #eaecef;
  background: #ffffff;
  padding: 9px 22px;
}

/* 主体部分 */
.MainArea {
  /* width: 500px; */
  width: 100%;
  /* margin: 0 auto; */
  margin-top: 10px;
  height: 300px;
  /* border-bottom: 10px solid #EAECEF; */
  background: #eaecef;
  border: 1px solid lightgray;
}

.Mainleft {
  /* width: 66%; */
  height: 100%;
  background: #ffffff;
}

.Vis {
  position: relative;
}

.menu {
  /*这个样式不写，右键弹框会一直显示在画布的左下角*/
  position: absolute;
  background: rgba(3, 3, 3, 0.6);
  border-radius: 5px;
  left: -99999px;
  top: -999999px;
  color: #fff;
  padding: 5px;
}

.LayOutBody {
  overflow-x: visible !important;
}

.headerTop {
  display: flex;
  justify-content: space-between;
}

.el-header,
.el-footer {
  background-color: #b3c0d1;
  color: #333;
  text-align: center;
  line-height: 60px;
}

.el-aside {
  background-color: #d3dce6;
  color: #333;
  text-align: center;
  line-height: 200px;
}

.el-main {
  background-color: #e9eef3;
  color: #333;
  text-align: center;
  line-height: 160px;
}

body > .el-container {
  margin-bottom: 90px;
}

.el-container:nth-child(5) .el-aside,
.el-container:nth-child(6) .el-aside {
  line-height: 260px;
}

.el-container:nth-child(7) .el-aside {
  line-height: 320px;
}

.WordExplains {
  display: flex;
  justify-content: left;
  font-size: 0.8rem;
}

.Wordname {
  white-space: nowrap;
}

.WordContent {
  margin-left: 5px;
}

.left {
  width: 100%;
  height: 100%;
  /* margin-bottom: 1.5vh; */
  border-top: 1px solid rgb(212, 212, 212);
  border-bottom: 1px solid rgb(202, 202, 202);
  background-color: #fff;
  /* padding: 0 10px 0 10px; */
  overflow: hidden;
}

.myDiv {
  width: 800px;
  height: 800px;
}

textarea {
  border: 1px solid lightgray;
  margin: 5px;
  border-radius: 5px;
}

#viz {
  width: 100%;
  height: 80%;
  border: 1px solid #f1f3f4;
  font: 22pt arial;
}

input {
  border: 1px solid #ccc;
}
</style>
