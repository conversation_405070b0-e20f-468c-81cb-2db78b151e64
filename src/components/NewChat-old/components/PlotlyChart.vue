<template>
  <div v-show="isShow">
    <div>
      <h3>分析图表如下</h3>
    </div>
    <div :id="id" style="width: 900px; max-width: 900px; height: 500px"></div>
  </div>
</template>

<script>
import Plotly from "plotly.js-dist";
export default {
  name: "",
  components: {},
  props: {
    // id: {
    //   typeof: String,
    //   default: "myPlot-",
    // },
    // data: {
    //   typeof: Array,
    //   default: () => [],
    // },
    // layout: {
    //   typeof: Object,
    //   default: () => {},
    // },
    // isShow: {
    //   typeof: Boolean,
    //   default: false,
    // },
  },
  data() {
    return {
      // isShow:false
      id:'myPlot-0',
      isShow:false
    };
  },
  computed: {},
  watch: {
    id: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            // this.draw()
          });
        }
      },
    },
  },
  created() {},
  mounted() {},
  methods: {
    draw(val) {
      this.isShow = true;
      const { id, data, layout } = val;
      this.id = id;
      let config = {
        toImageButtonOptions: {
          format: "png", // 设置图片导出格式
          filename: "image", //设置导出命名
          scale: 1, // 导出图片放大比例 1为不缩放
        },
        //移除图自带的工具
        modeBarButtonsToRemove: [
          "toggleSpikelines",
          "zoom2d",
          "pan2d",
          "select2d",
          "lasso2d",
          "autoScale2d",
        ],
        displaylogo: false,
      };
      //
      this.$nextTick(() => {
        Plotly.newPlot(id, data, layout, config);
        // Plotly.newPlot(this.id, data, layout, config);
        // this.$forceUpdate()
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
