<template>
  <div class="sidebar">
    <header>
      <ul class="flex" style="margin: 0; justify-content: space-around">
        <template v-if="isDatabaseHistory">
          <li
          >
            历史列表
          </li>
        </template>
        <template v-else>
          <template v-if="$store.state.common.files.length">
            <li
              v-for="(item, index) in optionsTopFiles"
              @click="handleTop(item, index)"
              :class="{ topCureent: index === topCureent }"
              :key="index"
            >
              {{ item.label }}
            </li>
          </template>
          <template v-else>
            <li
              v-for="(item, index) in optionsTop"
              @click="handleTop(item, index)"
              :class="{ topCureent: index === topCureent }"
              :key="index"
            >
              {{ item.label }}
            </li>
          </template>
        </template>
      </ul>
    </header>
    <div class="content">
      <template v-if="topCureent">
        <ul>
          <li
            v-for="(item, index) in optionsList"
            @click="handleList(item, index)"
            :class="{ listCureent: index === listCureent }"
            :key="index"
          >
            {{ item.label }}
          </li>
        </ul>
      </template>
      <template v-else>
        <ul>
          <li
            v-for="(item, index) in chatList"
            :key="item.id"
            :class="{
              current: index === currentIndex,
              'hover-class': isHovered && index === hoveredIndex,
            }"
            @click="handleClick(item, index)"
            @mouseover="handleMouseOver(index)"
            @mouseout="handleMouseOut(index)"
          >
            <span class="text">
              {{ item.query }}
            </span>

            <i
              @click="handleDel(item.id)"
              v-show="index === hoveredIndex"
              class="el-icon-delete"
            ></i>
          </li>
        </ul>
        <el-empty v-if="!chatList.length" description="暂无数据"></el-empty>
      </template>
    </div>
    <footer>
      <!-- <span @click="handleSetting">
        <i class="el-icon-setting mr10"></i>系统设置</span
      > -->
      <span v-if="currentRoleArr.length">角色配置：</span>
      <el-select
        style="width: 75%"
        v-if="currentRoleArr.length"
        v-model="currentRole"
        placeholder="请选择当前角色"
        clearable
        @change="handleRole"
      >
        <el-option
          v-for="item in currentRoleArr"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </footer>

    <el-dialog
      title="参数设置"
      :visible.sync="dialogVisible"
      width="30%"
      append-to-body
      :before-close="handleClose"
    >
      <div>
        <el-form
          label-position="right"
          label-width="100px"
          :model="formLabelAlign"
        >
          <el-form-item label="匹配向量数">
            <el-input-number
              v-model.number="formLabelAlign.top_k"
              :precision="0"
              label="匹配向量数"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="相关度">
            <el-slider
              v-model="formLabelAlign.score_threshold"
              :format-tooltip="formatTooltip"
            ></el-slider>
            <span style="color: #ccc; font-size: 12px"
              >取值范围在0-1之间,SCORE越小,相关度越高,取到1相当于不筛选,建议设置在0.5左右</span
            >
          </el-form-item>
          <el-form-item label="可选择模型">
            <el-select
              v-model="formLabelAlign.model_name"
              placeholder="请选择模型"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="可选择角色">
            <el-select
              v-model="formLabelAlign.prompt_name"
              placeholder="可选择角色"
            >
              <el-option
                v-for="item in promptList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitSeting">设 置</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import serviceKnowledge from "@/api/knowledge.js";
import { getUser } from "@/api/system/user";
import MarkdownIt from "markdown-it";
export default {
  name: "",
  components: {},
  props: {
    chatSet: {
      typeof: Object,
      default: () => {},
    },
    isDatabaseHistory: {
      typeof: Boolean,
      default: false,
    },
  },
  data() {
    return {
      formLabelAlign: {},
      currentIndex: null,
      dialogVisible: false,
      options: [],
      hoveredIndex: null, // 鼠标悬停项的索引
      isHovered: false, // 是否有元素被鼠标悬停
      chatList: [],
      promptList: [],
      topCureent: 0,
      optionsTopFiles:[
                {
          label: "对话列表",
          value: 0,
        },
        {
          label: "功能中心",
          value: 1,
        },
      ],
      optionsTop: [
        {
          label: "对话列表",
          value: 0,
        },
      ],
      optionsList: [
        {
          label: "文档概述",
          value: 0,
        },
        {
          label: "生成PPT",
          value: 1,
        },
      ],
      listCureent: null,
      summarize: "",
      generatePPT: "",
      currentRoleArr: [],
      currentRole: null,
    };
  },
  computed: {},
  watch: {
    chatSet: {
      handler(val) {
        if (val) {
          this.getList();
        }
      },
    },
  },
  created() {
    this.getConfigKey("summarize").then((response) => {
      this.summarize = response.msg;
    });
    this.getConfigKey("generatePPT").then((response) => {
      this.generatePPT = response.msg;
    });
    // 获取当前登录人的 ai角色与模型
    this.getUserAIRoleAndModel();
    // 获取知识库设置列表
    this.getKnowledgeSetList();
  },
  mounted() {},
  methods: {
    handleTop(item, index) {
      this.topCureent = index;
      this.listCureent = null;
      this.getList();
    },
    // 获取知识库设置列表
    async getKnowledgeSetList() {
      const { code, data } = await serviceKnowledge.getKnowledgeSetting();
      if (code === 200) {
        this.optionsList = data
          .filter((v) => v.type === "1")
          .map((v, index) => {
            return {
              label: v.knowledge_name,
              value: index,
              remark: v.remark,
              ...v,
            };
          });
      }
    },
    handleList(item, index) {
      this.listCureent = index;
      // const query = index ? this.generatePPT : this.summarize;
      this.$emit("getOptions", { query: item.remark, ...item });
    },
    handleRole() {
      this.$emit("getRole", { query: this.currentRole });
    },
    async getList() {
      let params = ''
      if (this.$store.state.common.isDatabaseChat) {
        params = '数据库对话'
      }
      if (this.$store.state.common.isGraphdataBaseChat) {
        params = '图数据库对话'
      }
      const { code, data } = await serviceKnowledge.getHistory({
        knowledgeName: this.isDatabaseHistory?params: this.chatSet.knowledge_name,
      });
      if (code === 200) {
        this.chatList = data;
      }
    },
    formatTooltip(val) {
      return val / 100;
    },
    parseMarkdown(text) {
      const md = new MarkdownIt({
        highlight: function (str, lang) {
          if (lang && hljs.getLanguage(lang)) {
            try {
              return hljs.highlight(lang, str).value;
            } catch (__) {}
          }

          return ""; // use external default escaping
        },
      });
      return md.render(text);
    },
    handleClick(item, index) {
      this.currentIndex = index;
      this.$emit("getHistory", { query: item.query, result:this.parseMarkdown(item.result),good:item.chat_like==='1'?true:false,bad:item.bad ,id:item.id});
    },
    handleSetting() {
      this.dialogVisible = true;
      let data = JSON.parse(JSON.stringify(this.chatSet));
      data.score_threshold = data.score_threshold * 100;
      this.formLabelAlign = data;
    },
    handleClose() {
      this.dialogVisible = false;
    },
    // 提交参数配置
    async submitSeting() {
      // 默认写死模型
      this.formLabelAlign.model_name = "chatglm4-9b";
      this.formLabelAlign.score_threshold =
        this.formLabelAlign.score_threshold / 100;
      let params = {
        ...this.chatSet,
        ...this.formLabelAlign,
      };
      const { code, data } = await serviceKnowledge.chatSetting(params);
      if (code === code) {
        this.$message.success("设置成功");
        this.dialogVisible = false;
        this.formLabelAlign = {
          top_k: 0,
          score_threshold: 0,
          model_name: "",
          prompt_name: "",
        };
        this.$emit("updateChatSet", params);
      }
    },
    // 鼠标移入
    handleMouseOver(index) {
      this.hoveredIndex = index;
      this.isHovered = true;
    },
    // 鼠标移出
    handleMouseOut(index) {
      if (this.hoveredIndex === index) {
        this.hoveredIndex = null;
        this.isHovered = false;
      }
    },
    // 删除列表信息
    async handleDel(id) {
      const { code, data } = await serviceKnowledge.getHistoryDeleted(id);
      if (code === 200) {
        this.getList();
      }
    },
    async getUserAIRoleAndModel() {
      // 模型列表
      //       const resModel= await serviceKnowledge.getModelList({
      //   pageNum: 1,
      //   pageSize: 9999,
      // });
      // 角色列表
      const { code, rows } = await serviceKnowledge.getPromptList({
        pageNum: 1,
        pageSize: 9999,
      });
      if (code === 200) {
        // 获取登录人配置角色id
        const res = await getUser(this.$store.state.user.userId);
        if (res.code === 200) {
          this.currentRoleArr = res.promptIds
            .map((v) => {
              const matchingRow = rows.find((i) => i.id === v);
              if (matchingRow) {
                return {
                  id: v,
                  label: matchingRow.promptDescription,
                  value: matchingRow.promptName,
                };
              }
            })
            .filter((obj) => obj !== null); // 使用 filter 去除 null 值（如果有的话）
          console.log(
            this.currentRoleArr.find(
              (v) => v.value === this.chatSet?.prompt_name
            )
          );

          this.currentRole =
            this.currentRoleArr.find(
              (v) => v.value === this.chatSet?.prompt_name
            )?.value || this.currentRoleArr[0]?.value;
          // this.form.modelIds = res.modelIds
          // this.form.promptIds = res.promptIds
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.sidebar {
  width: 20%;
  height: 100%;
  border-right: 1px solid #ccc;
  display: flex;
  flex-direction: column;
  padding: 10px;
  header {
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    .topCureent {
      color: #409eff;
      border-bottom: 5px solid #ccc;
      padding-bottom: 5px;
    }
  }
  .content {
    flex: 1;
    overflow: auto;
    margin: 10px 5px;
        overflow-x: hidden;
    li {
      width: 260px; /* 设置容器宽度 */
      font-size: 14px;
      line-height: 14px;
      cursor: pointer;
      margin-bottom: 10px;
      .text {
        width: 260px; /* 设置容器宽度 */
        white-space: nowrap; /* 防止文本换行 */
        overflow: hidden; /* 文本溢出容器时隐藏 */
        text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
      }

      padding: 5px 10px;
      display: flex;
      justify-content: space-between;
    }
    .current {
      background: linear-gradient(270deg, #c9ecff 0%, #468ff8 100%);
      border-radius: 13px;
      color: aliceblue;
    }
    .hover-class {
      /* 鼠标悬停时的样式 */
      background: linear-gradient(270deg, #c9ecff 0%, #468ff8 100%);
      border-radius: 13px;
      color: aliceblue;
    }
    .listCureent {
      background: linear-gradient(270deg, #c9ecff 0%, #468ff8 100%);
      border-radius: 13px;
      color: aliceblue;
    }
  }
  footer {
    max-height: 100px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
}
::v-deep .el-input--medium .el-input__inner {
  height: 38px !important;
}
</style>
