<template>
  <div class="tools_Ai">
    <div class="ask-operations">
      <div
        class="operations_item copy tool-item"
        :data-clipboard-target="'#text-' + index"
        title="复制文本"
      >
        <img :src="require('@/assets/copy.svg')" />
        <span @click="copyFun">复制</span>
      </div>
      <div
        class="operations_item tool-item"
        title="删除"
        @click="delAns(index)"
      >
        <img :src="require('@/assets/trash.svg')" />
        <span>删除</span>
      </div>
      <div
        class="operations_item tool-item"
        v-show="item.isStop"
        title="暂停"
        @click="stop(index)"
      >
        <img :src="require('@/assets/暂停.svg')" />
        <span>暂停</span>
      </div>
      <div
        class="operations_item tool-item"
        title="导出文件"
        v-show="item.isComplete"
        @click="fileGeneration(index)"
      >
        <img :src="require('@/assets/文件生成.svg')" />
        <span>导出文件</span>
      </div>
      <template v-if="!showAppend">
        <div
          class="operations_item tool-item"
          title="重新生成"
          v-show="item.isComplete"
          @click="regenerate(index, item)"
        >
          <img :src="require('@/assets/重新生成.svg')" />
          <span>重新生成</span>
        </div>
      </template>

      <div
        class="operations_item tool-item"
        title="清除会话"
        v-show="item.isRecords"
        @click="clearRecords(index, item)"
      >
        <img :src="require('@/assets/清除记录.svg')" />
        <span>清除会话</span>
      </div>
      <div
        class="operations_item tool-item"
        title="追加"
        @click="append(index, item)"
        v-if="showAppend"
      >
        <img :src="require('@/assets/追加.svg')" />
        <span>追加</span>
      </div>
      <div
        v-if="!showAppend"
        class="operations_item tool-item"
        :title="item.isVoicePlay ? '语音暂停' : '语音播放'"
        @click="voicePlayBack(index, item)"
      >
        <img v-if="item.isVoicePlay" :src="require('@/assets/语音暂停.svg')" />
        <img v-else :src="require('@/assets/语音播放.svg')" />
        <span>{{ item.isVoicePlay ? "语音暂停" : "语音播放" }}</span>
      </div>
      <div
        class="operations_item tool-item"
        title="表格"
        @click="tableShow(index, item)"
        v-if="istableShow"
      >
        <img :src="require('@/assets/表格.svg')" />
        <span>表格</span>
      </div>
    </div>
    <template v-if="!showAppend">
      <div class="thumbs-up" v-show="item.id">
        <div class="operations_item" @click="handleGood(index, item)">
          <img v-if="item.good" :src="require('@/assets/赞-选中.svg')" />
          <img v-else :src="require('@/assets/赞.svg')" />
        </div>
      </div>
    </template>
    <TableShow ref="TableShowRef" />
  </div>
</template>

<script>
const synth = window.speechSynthesis;
const speech = new SpeechSynthesisUtterance();
import { parseMarkdown, extractPlainTextFromHTML } from "../utils";
import ClipboardJS from "clipboard";
import TableShow from "./TableShow";
export default {
  name: "",
  components: { TableShow },
  props: {
    item: {
      typeof: String,
      default: "",
    },
    index: {
      typeof: String,
      default: "",
    },
    chatList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    showAppend: {
      typeof: Boolean,
      default: false,
    },
    istableShow: {
      typeof: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  computed: {},
  watch: {
    item: {
      handler(val) {
        console.log(val);
        this.$forceUpdate();
      },
      deep: true,
      immediate: true,
    },
  },
  created() {},
  mounted() {
    window.addEventListener("beforeunload", function (event) {
      console.log("刷新");
      // 暂停所有语音播报
      synth.cancel();

      // 如果你想给用户一个更明确的提示，可以使用以下代码（但注意，现代浏览器可能会限制或忽略这种自定义消息）
      // event.returnValue = '语音播报已取消，因为页面正在刷新。'; // 这条语句在某些浏览器中可能不起作用
    });
  },
  methods: {
    update() {
      this.$forceUpdate();
    },
    delAns(index) {
      this.$emit("delAns", index);
    },
    handleGood(index, item) {
      this.$emit("handleGood", { ...item, index });
    },
    stop(index) {
      this.$emit("stop", index);
    },
    copyFun() {
      this.$emit("copyFun");
    },
    fileGeneration(index) {
      this.$emit("fileGeneration", index);
    },
    regenerate(index, item) {
      console.log(item);
      this.$emit("regenerate", item.prompt);
    },
    clearRecords(index, item) {
      item.isRecords = false;
      this.$emit("clearRecords", index);
    },
    append(index, item) {
      this.$emit("append", item);
      // this.handleGood(index, item);
    },
    tableShow(index, item) {
      console.log(item);
      if (item.tabledata && item.tabledata.length) {
        let list = item.tabledata.map((v, index) => {
          return {
            index: index + 1,
            content: v,
          };
        });
        this.$nextTick(() => {
          this.$refs.TableShowRef.tableShow(list);
        });
      } else {
        this.$message.warning("表格数据为空");
      }
    },
    voicePlayBack(index, item) {
      console.log(item);
      synth.cancel();
      if (item.isVoicePlay) {
        synth.cancel(); // 取消当前播放
      } else {
        console.log(extractPlainTextFromHTML(item.appentText));
        speech.text = extractPlainTextFromHTML(item.appentText); // 文字内容
        // speech.text = '测试语音完成测试语音完成测试语音完成测试语音完成测试语音完成测试语音完成测试语音完成测试语音完成测试语音完成测试语音完成测试语音完成测试语音完成测试语音完成' ; // 文字内容
        speech.lang = "zh-CN"; // 使用的语言:中文
        speech.volume = 1; // 声音音量：1
        speech.rate = 1; // 语速：1
        speech.pitch = 1; // 音高：1
        synth.speak(speech); // 播放
      }

      Object.assign(item, { isVoicePlay: !item.isVoicePlay });

      // 调取语音播报完毕
      speech.onend = () => {
        console.log("end");
        item.isVoicePlay = false;
        console.log(item.isVoicePlay);
        this.$forceUpdate();
      };
      this.$forceUpdate();
    },
  },
  destroyed() {
    synth.cancel(); // 取消当前播放
  },
};
</script>

<style scoped lang="scss">
.tools_Ai {
  display: flex;
  margin-top: 20px;
  .ask-operations {
    display: flex;
  }
  img {
    width: 25px;
  }
  .tool-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-right: 15px;
  }
  .thumbs-up {
    cursor: pointer;
  }
}
</style>
