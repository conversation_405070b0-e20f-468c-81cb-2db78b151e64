<template>
  <div class="">
    <el-dialog
      title="数据展示"
      :visible.sync="dialogVisible"
      width="60%"
      :before-close="handleClose"
      append-to-body
    >
      <template v-if="tableList.length > 10">
        <div style="display: flex">
          <el-table :data="tableList" style="width: 100%; height: 100%">
            <el-table-column prop="index" label="序号" width="50">
            </el-table-column>
            <el-table-column prop="content" label="内容"> </el-table-column>
          </el-table>
          <el-table :data="tableListright" style="width: 100%; height: 100%">
            <el-table-column prop="index" label="序号" width="50">
            </el-table-column>
            <el-table-column prop="content" label="内容"> </el-table-column>
          </el-table>
        </div>
      </template>
      <template v-else>
        <el-table
          :data="tableList"
          style="width: 100%; height: 610px; overflow: auto"
        >
          <el-table-column prop="index" label="序号" width="50">
          </el-table-column>
          <el-table-column prop="content" label="内容"> </el-table-column>
        </el-table>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "",
  components: {},
  props: {},
  data() {
    return {
      dialogVisible: false,
      tableList: [],
      tableListright: [],
      items: [],
      isRange: false,
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    handleClose(done) {
      done();
    },
    tableShow(tabledata) {
      console.log(tabledata);
      if (tabledata.length < 10) {
        this.tableList = tabledata;
      } else {
        let length = tabledata.length;
        this.tableList = tabledata.slice(0, length / 2);
        this.tableListright = tabledata.slice(length / 2, length);
        this.tableListright = this.tableListright.map((v, index) => {
          return {
            index: this.tableList.length * 1 + 1 + index,
            content: v.content,
          };
        });
        console.log(this.tableList);
        console.log(this.tableListright);
      }
      this.dialogVisible = true;
    },
  },
};
</script>

<style scoped lang="scss"></style>
