<template>
  <div class="">
    <el-dialog
      title="常见问题列表维护"
      :visible.sync="dialogVisible"
      width="70%"
      v-if="dialogVisible"
      :append-to-body="true"
      :before-close="handleClose"
    >
      <Help ref="helpRef" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleClose">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Help from "@/views/techdocmanage/help";
export default {
  name: "",
  components: { Help },
  props: {},
  data() {
    return {
      dialogVisible: false,
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    open(knowledgeName) {
      this.dialogVisible = true;
      setTimeout(() => {
        this.$nextTick(() => {
          this.$refs.helpRef.initData(knowledgeName);
        });
      }, 500);
    },
    handleClose() {
      this.dialogVisible = false;
      this.$emit("updataHelpList");
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .el-dialog__body {
  height: calc(100vh - 300px);
}
::v-deep .el-table__body-wrapper {
  height: calc(100vh - 550px) !important;
  overflow-y: auto !important;
}
::v-deep .el-table{
      height: calc(-500px + 100vh) !important;
}
</style>
