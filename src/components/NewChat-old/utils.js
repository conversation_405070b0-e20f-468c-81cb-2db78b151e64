import "highlight.js/styles/github.css";
import MarkdownIt from "markdown-it";
// docs去重
// export const processDocs = (docs) => {
//   const uniqueDocs = Array.from(new Set(docs));
//   return uniqueDocs.map((doc) => {
//     const [fileName, link] = doc.split(",");
//     return { fileName, link };
//   });
// };
export const processDocs = (docs) => {
  // 使用map和箭头函数来转换数组
  const newDocs = docs.map((doc) => {
    const [fileName, link, ...contentParts] = doc
      .split(",")
      .map((part) => part.trim());
    const content = contentParts.join(",");
    return { fileName, link, content };
  });

  // 使用Set和filter来去重
  const seenFileNames = new Set();
  const uniqueDocs = newDocs.filter((doc) => {
    if (!seenFileNames.has(doc.fileName)) {
      seenFileNames.add(doc.fileName);
      return true;
    }
    return false;
  });
  console.log("uniqueDocs", uniqueDocs);
  return uniqueDocs;
};

// 路由大小写转换
export const firstLetterToUpperCase = (str) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};
// 转换成md格式
export const parseMarkdown = (text) => {
  const md = new MarkdownIt({
    highlight: function (str, lang) {
      if (lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlight(lang, str).value;
        } catch (__) {}
      }

      return ""; // use external default escaping
    },
  });
  return md.render(text);
};

const neo4j = require("neo4j-driver");
const processResult = (records) => {
  let SQLShow = false;
  let isCountStr = false;
  let tabledataShow = false;
  let count = null;
  let tabledata = [];

  if (records && records.length > 0) {
    const lastKey = records[0].keys[records[0].keys.length - 1].toLowerCase();
    if (lastKey.startsWith("count")) {
      isCountStr = true;
      count = records[0]?.get(lastKey)?.toNumber(); // 使用 get 方法获取字段值
    } else {
      SQLShow = true;
      const tableColumns = records[0].keys.map((v) => ({ prop: v, label: v }));
      tabledata = records.map((record) => {
        let obj = {};
        tableColumns.forEach((column) => {
          obj[column.prop] = record.get(column.prop).toString(); // 使用 get 方法并转换为字符串
        });
        // 假设最后一个字段是包含 properties 的对象，需要特殊处理
        if (lastKey in obj) {
          const properties = JSON.parse(obj[lastKey]);
          if (properties?.ELEMENT_NAME) {
            obj = { ELEMENT_NAME: properties.ELEMENT_NAME };
          }
        }
        return obj;
      });
      tabledataShow = true;
    }
  }

  return {
    SQLShow,
    isCountStr,
    tabledataShow,
    count,
    tabledata,
  };
};
export const executeCypher = async (query) => {
  // 创建驱动实例
  const driver = neo4j.driver(
    "bolt://192.168.0.150:7688",
    neo4j.auth.basic("neo4j", "a1b2c3d4")
  );
  let session = driver.session();

  // try {
  const result = await session.run(query, {});
  const records = result.records;
  console.log("neo4j 查询结果", records);

  const response = processResult(records);
  // const response = 2222;
  response.isEmty = records.length === 0 ? 1 : 0;

  // 假设 draw 方法是某个 Vue 组件的方法，这里需要根据实际情况调整
  // 如果 draw 方法确实存在，并且需要传递 query，则保留下面的代码
  // if (response.tabledataShow) {
  //   me.$refs.GraphChart.at(-1).draw(query);
  // }

  await session.close();
  return response;
  // } catch (error) {
  //   await driver.close();
  //   // throw error; // 重新抛出错误，以便调用者可以处理
  // }
};

// export const processAnswerWithFileNames=(answer, docResult)=> {
//   // 使用正则表达式找到所有由《》包裹的文件名
//   const filePattern = /《(.*?)》/g;
//   let processedAnswer = answer;
//   let fileNames = [];
//   let docResultIndexes = {}; // 用于存储文件名和其在docResult中的索引（如果有的话）

//   let match;
//   while ((match = filePattern.exec(answer)) !== null) {
//     // 移除文件名中的空格
//     const fileNameWithoutSpaces = match[1].replace(/\s+/g, "");
//     fileNames.push(fileNameWithoutSpaces);

//     // 查找文件名在docResult中的索引（如果不存在则为-1）
//     if (!docResultIndexes[fileNameWithoutSpaces]) {
//       const index = docResult.findIndex((v) =>
//         v.resultName.includes(fileNameWithoutSpaces)
//       );
//       docResultIndexes[fileNameWithoutSpaces] = index !== -1 ? index : -1;
//     }

//     // 根据是否存在于docResult中设置字体颜色
//     const fontColor =
//       docResultIndexes[fileNameWithoutSpaces] !== -1
//         ? "#409eff"
//         : "#f56e48";

//     // 注意：这里原本使用 fileNames.length - 1 作为类名可能不是最佳实践，
//     // 因为它依赖于 fileNames 数组的长度，这可能会导致类名重复或不一致。
//     // 如果每个文件名都应该是唯一的类名，你应该使用文件名本身（可能是处理过的无空格版本）。
//     // const className = `FILE_${fileNameWithoutSpaces}`; // 使用文件名作为类名
//     const className = `FILE_${fileNames.length - 1}`; // 使用文件名作为类名

//     // 生成占位符
//     const placeholder = `<span style='color:${fontColor};cursor: pointer;' class='${className}'> 《 ${match[1]} 》 </span>`;
//     // 使用正则表达式和函数来确保只替换当前的匹配项（避免全局替换影响后续文本）
//     // 但是，由于我们在循环中逐个处理匹配项，并且每次替换后都会更新 processedAnswer，
//     // 所以实际上不需要担心全局替换的问题。然而，为了更安全，我们可以使用更精确的正则表达式。
//     // 下面的代码已经足够安全，因为 replace 方法只替换第一个匹配的实例（由于我们使用了新的 RegExp）。
//     // 但是，为了更清晰的意图，我们可以稍微调整正则表达式，以确保它只匹配当前的文件名（考虑边界情况）。
//     const escapeRegExp = (str) => str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // 转义正则表达式特殊字符的函数
//     const safeFileName = escapeRegExp(match[1]); // 转义后的文件名
//     processedAnswer = processedAnswer.replace(
//       new RegExp(`《${safeFileName}》`),
//       placeholder
//     );
//   }

//   // 返回处理后的答案和文件名索引映射
//   return {
//     fileNames,
//     processedAnswer,
//     docResultIndexes
//   };
// }
export const processAnswerWithFileNames = (answer) => {
  // 使用正则表达式找到所有由《》包裹的文件名
  const filePattern = /《(.*?)》/g;
  let processedAnswer = answer;
  let fileNames = [];
  let match;
  while ((match = filePattern.exec(answer)) !== null) {
    // 移除文件名中的空格
    const fileNameWithoutSpaces = match[1].replace(/\s+/g, "");
    fileNames.push(fileNameWithoutSpaces);

    // 查找文件名在docResult中的索引（如果不存在则为-1）
    if (!docResultIndexes[fileNameWithoutSpaces]) {
      const index = docResult.findIndex((v) =>
        v.resultName.includes(fileNameWithoutSpaces)
      );
      docResultIndexes[fileNameWithoutSpaces] = index !== -1 ? index : -1;
    }

    // 根据是否存在于docResult中设置字体颜色
    const fontColor =
      docResultIndexes[fileNameWithoutSpaces] !== -1 ? "#409eff" : "#f56e48";

    // 注意：这里原本使用 fileNames.length - 1 作为类名可能不是最佳实践，
    // 因为它依赖于 fileNames 数组的长度，这可能会导致类名重复或不一致。
    // 如果每个文件名都应该是唯一的类名，你应该使用文件名本身（可能是处理过的无空格版本）。
    // const className = `FILE_${fileNameWithoutSpaces}`; // 使用文件名作为类名
    const className = `FILE_${fileNames.length - 1}`; // 使用文件名作为类名

    // 生成占位符
    const placeholder = `<span style='color:${fontColor};cursor: pointer;' class='${className}'> 《 ${match[1]} 》 </span>`;
    // 使用正则表达式和函数来确保只替换当前的匹配项（避免全局替换影响后续文本）
    const escapeRegExp = (str) => str.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"); // 转义正则表达式特殊字符的函数
    const safeFileName = escapeRegExp(match[1]); // 转义后的文件名
    processedAnswer = processedAnswer.replace(
      new RegExp(`《${safeFileName}》`),
      placeholder
    );
  }

  // 返回处理后的答案和文件名索引映射
  return {
    fileNames,
    processedAnswer,
    docResultIndexes,
  };
};

// 检查是否是html语言
export const isHtml = (str) => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(str, "text/html");
  return Array.from(doc.body.childNodes).some((node) => node.nodeType === 1);
};

export const extractPlainTextFromHTML = (htmlString) => {
  // 创建一个 DOMParser 实例
  const parser = new DOMParser();
  // 解析 HTML 字符串为 DOM 文档
  const doc = parser.parseFromString(htmlString, "text/html");

  // 创建一个函数来递归遍历 DOM 树并提取文本内容
  function getTextFromElement(element) {
    let text = "";

    // 遍历当前元素的所有子节点
    Array.from(element.childNodes).forEach((node) => {
      // 如果节点是文本节点，则添加其文本内容
      if (node.nodeType === Node.TEXT_NODE) {
        text += node.textContent.trim();
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        // 如果是元素节点，则递归提取其文本内容
        text += getTextFromElement(node);
      }
    });

    // 返回提取的文本内容
    return text;
  }

  // 提取文档的 body 部分的文本内容
  const plainText = getTextFromElement(doc.body);

  // 返回最终的纯文本内容
  return plainText;
};
