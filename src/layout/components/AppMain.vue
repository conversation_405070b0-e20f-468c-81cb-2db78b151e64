<template>
  <section
    class="app-main"
    :style="{
      backgroundColor: sideTheme === 'theme-dark' ? variables.allBg : '#f2f3f5',
      height: systemRh() + 'px',
    }"
  >

    <Chat :oldChat="oldChat" ref="chat" @closeOnClickModal="closeOnClickModal" @handleMinimize="handleMinimize"/>


    <div class="mini-dialog" v-show="chatDialog.isShrink">
      <Robot @click.native="showDialog"/>
      <!-- <el-button type="primary" @click="showDialog">聊天</el-button> -->
    </div>
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view
          :style="{
            backgroundColor:
              sideTheme === 'theme-dark'
                ? variables.cont
                : variables.menuLightBg,
          }"
          :key="key"
        />
      </keep-alive>
    </transition>
  </section>
</template>

<script>
import ZDialog from "@/components/ZDialog";
import Robot from "@/components/Robot/index.vue";
import DatabaseChat from "@/components/Chat/databaseChat.vue";
import GraphdataBaseChat from "@/components/Chat/graphdataBaseChat.vue";
// import Chat from "@/components/Chat/index.vue";
import {mapGetters} from "vuex";
import variables from "@/assets/styles/variables.scss";
import serviceKnowledge from "@/api/knowledge.js";
import {getUser} from "@/api/system/user";
import MedicalAtlasChat from "@/components/Chat/medicalAtlasChat.vue";
import Chat from '@/components/NewChat'

export default {
  name: "AppMain",
  components: {
    ZDialog,
    Chat,
    Robot,
    DatabaseChat,
    GraphdataBaseChat,
    MedicalAtlasChat,
  },
  data() {
    return {
      // 聊天对话框配置
      chatDialog: {
        titleChat: "知识库AI助手",
        openChat: false,
        isShrink: true,
        chatParams: {
          knowledge_base_name: "",
          knowledgeList: [],
        },
      },
      knowledgeValue: "",
      isMinimize: false,
      oldChat: ''
    };
  },
  watch: {
    "$store.state.common.isMinimize": {
      handler(val, old) {
        console.log('测试放大缩小', val);
        if (val) {

          this.$refs.zdialog.minimize();
        }
      },
    },
    "$store.state.common.chat": {
      handler(val, old) {
        console.log('个人知识库新', val);
        console.log('个人知识库旧', old);
        this.oldChat = old
        // if (val) {
        //   this.$refs.zdialog.minimize();
        // }
      }, immediate: true
    },
  },
  computed: {
    ...mapGetters(["sideTheme"]),
    cachedViews() {
      return this.$store.state.tagsView.cachedViews;
    },
    key() {
      return this.$route.path;
    },
    variables() {
      return variables;
    },
  },
  mounted() {
    let _this = this;
    $(window).resize(function () {
      $(".app-main").css({
        height: _this.systemRh() + "px",
      });
    });
    this.$watch(
      () => this.$store.state.common.chat,
      (val) => {
        if (val) {

          this.showDialog();
        }
      }
    );
  },
  methods: {
    handleMinimize() {
      this.chatDialog.isShrink = true;
    },
    closeOnClickModal(val) {

      this.chatDialog.isShrink = true;
      this.$store.commit("common/CHAT", "");
      this.$store.commit("common/FILES", "");
    },
    shrinkDialog() {
      this.chatDialog.openChat = false;
      this.chatDialog.isShrink = true;
      this.$store.commit("common/CHAT", "");
      this.$store.commit("common/FILES", "");
      this.$store.commit("common/CHATLOADING", false);
      this.$store.commit("common/isDatabaseChat", false);
      this.$store.commit("common/isGraphdataBaseChat", false);
      this.$store.commit("common/isMedicalAtlasChat", false);
      // this.$store.commit("common/cardListFn", false);
    },
    // 获取知识库列表
    async getKnowledgeList() {
      const {code, data} = await serviceKnowledge.getKnowledgeList();
      if (code === 200) {
        const res = await getUser(this.$store.state.user.userId);
        const newData = res.modelIds
        // this.$store.state.user.userId === "1" ? data : res.modelIds;
        const nickName = this.$store.state.user.nickName || "";
        this.knowledgeList = newData
          .map((v, index) => {
            return {
              label: v,
              value: index,
            };
          })
          .filter((item) => {
            return !(
              (item.label.startsWith("个人文档-") &&
                item.label !== `个人文档-${nickName}`) ||
              item.label.startsWith("template-")
            );
          });
        const value = this.$store.state.common.chat;
        this.knowledgeValue = value ? value : newData[0];
        if (this.knowledgeValue === "数据库对话") {
          this.$store.commit("common/isDatabaseChat", true);
          this.$store.commit("common/isGraphdataBaseChat", false);
          this.$store.commit("common/isMedicalAtlasChat", false);
        } else if (this.knowledgeValue === "图数据库对话") {
          this.$store.commit("common/isDatabaseChat", false);
          this.$store.commit("common/isGraphdataBaseChat", true);
          this.$store.commit("common/isMedicalAtlasChat", false);
        } else if (this.knowledgeValue === "医药图谱") {
          this.$store.commit("common/isDatabaseChat", false);
          this.$store.commit("common/isGraphdataBaseChat", false);
          this.$store.commit("common/isMedicalAtlasChat", true);
        } else {
          this.$store.commit("common/isDatabaseChat", false);
          this.$store.commit("common/isGraphdataBaseChat", false);
          this.$store.commit("common/isMedicalAtlasChat", false);
        }
      }
    },
    showDialog() {
      this.chatDialog.isShrink = false;


      // if (this.$store.state.common.files.length>0&&this.oldChat) {
      //         this.$store.commit("common/CHAT",this.oldChat);
      // }

      if (this.$store.state.common.isEnterpriseKnowledge) {
        this.$refs.chat.handleMinimize()
      }
      this.$refs.chat.open()
    },
  },
};
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  // min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
  padding: 0 20px;
  // overflow-y: auto !important;
  // .adminBoard {
  //   height: 891px;
  //   overflow-y: auto !important;
  // }
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    // min-height: calc(100vh - 93px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}

.mini-dialog {
  // position: absolute;
  //   bottom: -10%;
  //   right: -5%;
  // z-index: 3000;
}

.chat-dialog {
  // z-index: 1000 !important;
}

::v-deep .chat-dialog .el-dialog__body {
  height: 80vh !important;
  overflow: auto;
  padding: 0px !important;
}

::v-deep .chat-dialog .el-dialog {
  background: #d9e3f4 !important;
}

::v-deep .chat-dialog .el-dialog__header {
  border-bottom: 1px solid #fff;
}

.header-logo {
  width: 30px;
  height: 30px;
  object-fit: contain;
  cursor: pointer;
  display: flex;
  margin-right: 10px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}

.el-select-dropdown {
  z-index: 1500 !important;
}
</style>
