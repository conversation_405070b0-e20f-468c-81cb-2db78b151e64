import knowledgeApi from '@/api/knowledge.js'
import { getToken } from '@/utils/auth'
import { bus } from '@/bus'

// 格式化历史记录项 - 直接使用后端字段
function formatHistoryItem(item) {
  if (!item) return null
  return {
    ...item,
    liked: item.chat_like === 1,
    timestamp: new Date(item.create_time).getTime(),
    done: true
  }
}

// 获取聊天配置参数
function getChatConfig(settings) {
  return {
    model_name: settings.model_name,
    prompt_name: settings.prompt_name,
    score_threshold: settings.score_threshold,
    top_k: settings.top_k,
    temperature: settings.temperature,
    mix_type: settings.mix_type,
    max_tokens: settings.max_tokens
  }
}

const state = {
  // 核心业务数据
  currentKnowledge: null,
  messages: [],
  activeFiles: [],

  // 聊天配置（保存历史记录需要）
  chatSettings: {},

  // 流式聊天状态（跨组件共享）
  streaming: {
    active: false,
    messageId: null,
    answer: ''
  },

  // 发送状态（多个组件需要）
  isSending: false,

  // 流式连接管理
  abortController: null
}

const getters = {
  canSendMessage: state => !state.isSending && !state.streaming.active,
  isStreaming: state => state.streaming.active,
  currentKnowledgeId: state => state.currentKnowledge?.id || null,
  currentKnowledgeName: state => state.currentKnowledge?.label || null,

  // 添加获取排序后的消息列表
  sortedMessages: state => [...state.messages].sort((a, b) => a.timestamp - b.timestamp),

  // 获取最新的用户消息
  lastUserMessage: state => {
    const userMessages = state.messages.filter(msg => msg.type === 'user');
    return userMessages[userMessages.length - 1];
  },

  // 获取最新的AI消息
  lastAiMessage: state => {
    const aiMessages = state.messages.filter(msg => msg.type === 'assistant');
    return aiMessages[aiMessages.length - 1];
  }
}

const mutations = {
  SET_CURRENT_KNOWLEDGE(state, knowledge) {
    state.currentKnowledge = knowledge
  },
  SET_ACTIVE_FILES(state, files) {
    state.activeFiles = files || []
  },
  SET_CHAT_SETTINGS(state, settings) {
    state.chatSettings = settings || {}
  },
  ADD_MESSAGE(state, message) {
    const newMessage = {
      id: message.id || Date.now() + Math.random(),
      timestamp: message.timestamp || Date.now(),
      docs: [], // 确保 docs 字段被 Vue 响应式系统正确跟踪
      ...message
    }
    state.messages.push(newMessage)
  },
  UPDATE_MESSAGE(state, { id, updates }) {
    const index = state.messages.findIndex(msg => msg.id === id)
    if (index !== -1) {
      // 保护 docs 字段不被意外覆盖
      if (!updates.hasOwnProperty('docs') && state.messages[index].docs) {
        updates = { ...updates, docs: state.messages[index].docs }
      }
      Object.assign(state.messages[index], updates)
    }
  },
  DELETE_MESSAGE(state, messageId) {
    state.messages = state.messages.filter(msg => msg.id !== messageId)
  },
  CLEAR_MESSAGES(state) {
    state.messages = []
  },
  SET_SENDING(state, status) {
    state.isSending = status
  },
  SET_STREAMING(state, streaming) {
    state.streaming = { ...state.streaming, ...streaming }
  },
  RESET_STREAMING(state) {
    state.streaming = { active: false, messageId: null, answer: '' }
  }
}

const actions = {
  reset({ commit, state }) {
    commit('CLEAR_MESSAGES')
    commit('RESET_STREAMING')
    commit('SET_SENDING', false)
    commit('SET_CURRENT_KNOWLEDGE', null)
    commit('SET_ACTIVE_FILES', [])
    commit('SET_CHAT_SETTINGS', {})

    if (state.abortController) {
      state.abortController.abort()
      state.abortController = null
    }
  },

  async setCurrentKnowledge({ commit, state }, knowledge) {
    if (!knowledge) {
      throw new Error('知识库信息不能为空')
    }

    // 如果是切换到不同的知识库
    if (state.currentKnowledge && state.currentKnowledge.id !== knowledge.id) {
      const oldKnowledge = state.currentKnowledge;
      console.log(`正在从 "${oldKnowledge?.label}" 切换到 "${knowledge?.label}"`);
    }

    commit('SET_CURRENT_KNOWLEDGE', knowledge)

    // 获取知识库配置
    if (knowledge.label) {
      try {
        const response = await knowledgeApi.getChatSetting({ knowledgeName: knowledge.label })
        if (response.code === 200 && response.data?.length > 0) {
          const settings = {
            ...response.data[0],
            knowledge_name: knowledge.label
          };
          commit('SET_CHAT_SETTINGS', settings)

          // 根据新知识库的历史记录配置决定是否保留现有消息
          const shouldUseHistory = settings.history === "1" || settings.history === 1;
          console.log('知识库历史配置:', settings.history, '是否启用历史:', shouldUseHistory);

          // 如果新知识库不启用历史记录，则清除现有消息
          if (!shouldUseHistory) {
            commit('CLEAR_MESSAGES');
          }
        }
      } catch (error) {
        console.error('获取知识库配置失败:', error)
        commit('SET_CHAT_SETTINGS', { knowledge_name: knowledge.label })
      }
    }

    // 重置流状态
    commit('RESET_STREAMING')
    commit('SET_ACTIVE_FILES', [])

    // 清空当前对话并开启新对话
    commit('CLEAR_MESSAGES');

    if (state.abortController) {
      state.abortController.abort()
      state.abortController = null
    }
  },

  async sendMessage({ commit, state, getters }, { text, knowledgeId }) {
    if (!text.trim() || state.isSending) return

    return new Promise((resolve, reject) => {
      try {
        commit('SET_SENDING', true)

        commit('ADD_MESSAGE', {
          type: 'user',
          answer: text.trim(),
          knowledgeId: knowledgeId || getters.currentKnowledgeId
        })

        const aiMessage = {
          type: 'assistant',
          answer: '',
          isStreaming: true,
          done: false
        }
        commit('ADD_MESSAGE', aiMessage)
        const newAiMessageId = state.messages[state.messages.length - 1].id

        commit('SET_STREAMING', {
          active: true,
          messageId: newAiMessageId,
          answer: ''
        })

        const currentKnowledge = state.currentKnowledge
        if (!currentKnowledge) {
          throw new Error('请先选择知识库')
        }

        // 格式化历史记录 - 修复历史记录格式问题
        const formattedHistory = state.messages
          .filter(msg => !msg.loading && !msg.error && (msg.answer || msg.content))
          .slice(-10)
          .map(msg => ({
            role: msg.type === 'user' ? 'user' : 'assistant',
            content: msg.answer || msg.content || ''
          }))
          .filter(msg => msg.content.trim() !== ''); // 过滤掉空内容的消息

        // 根据知识库配置决定是否启用历史对话
        const shouldUseHistory = state.chatSettings.history === "1" || state.chatSettings.history === 1;
        const historyData = shouldUseHistory ? formattedHistory : [];

        console.log('chat-v3 知识库历史配置:', state.chatSettings.history, '是否启用历史:', shouldUseHistory, '历史数据:', historyData);

        const config = getChatConfig(state.chatSettings)

        const requestBody = {
          query: text,
          knowledge_base_name: currentKnowledge.label,
          stream: true,
          history: historyData, // 使用根据配置决定的历史数据
          ...config
        }

        state.abortController = new AbortController()

        fetch('/stream-api/chat/knowledge_base_chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + getToken()
          },
          body: JSON.stringify(requestBody),
          signal: state.abortController.signal
        })
          .then(response => {
            if (!response.ok) {
              throw new Error(`请求失败: ${response.status}`)
            }

            const reader = response.body.getReader()
            const decoder = new TextDecoder()
            let buffer = '' // 添加缓冲区处理不完整的JSON

            function readStream() {
              return reader.read().then(({ done, value }) => {
                if (done) {
                  commit('SET_STREAMING', { active: false, messageId: null, answer: '' })
                  commit('UPDATE_MESSAGE', {
                    id: newAiMessageId,
                    updates: { isStreaming: false, done: true }
                  })

                  const userMessage = state.messages.find(m => m.type === 'user' && m.answer === text.trim())
                  const aiMessage = state.messages.find(m => m.id === newAiMessageId)

                  if (userMessage && aiMessage && aiMessage.answer) {
                    const saveConfig = getChatConfig(state.chatSettings)
                    knowledgeApi.saveDatabase({
                      knowledge_name: currentKnowledge.label,
                      query: userMessage.answer,
                      result: aiMessage.answer,
                      type: state.chatSettings?.label || "",
                      generate: 0,
                      ...saveConfig
                    }).then(response => {
                      if (response.code === 200) {
                        commit('UPDATE_MESSAGE', {
                          id: newAiMessageId,
                          updates: { historyId: response.msg }
                        })
                        bus.$emit('refresh-chat-history')
                      }
                    }).catch(error => {
                      console.error('保存历史记录失败:', error)
                    })
                  }

                  commit('SET_SENDING', false)
                  resolve()
                  return
                }

                const chunk = decoder.decode(value, { stream: true })
                buffer += chunk

                // 处理可能的多个JSON对象，按行分割
                const lines = buffer.split('\n')
                buffer = lines.pop() || '' // 保留最后一个可能不完整的行

                for (const line of lines) {
                  if (line.startsWith('data: ')) {
                    try {
                      const jsonStr = line.slice(6).trim()

                      if (jsonStr && jsonStr !== '[DONE]') {
                        const data = JSON.parse(jsonStr)
                        const updateData = {}

                        // 处理 answer 字段
                        if (data.answer) {
                          const currentAnswer = state.streaming.answer + data.answer
                          commit('SET_STREAMING', {
                            active: true,
                            messageId: newAiMessageId,
                            answer: currentAnswer
                          })
                          updateData.answer = currentAnswer
                        }

                        // 处理 docs 字段 - 独立于 answer
                        if (data.docs && data.docs.length > 0) {
                          updateData.docs = data.docs
                        }

                        // 只有当有数据需要更新时才调用 UPDATE_MESSAGE
                        if (Object.keys(updateData).length > 0) {
                          commit('UPDATE_MESSAGE', {
                            id: newAiMessageId,
                            updates: updateData
                          })
                        }
                      }
                    } catch (e) {
                      console.warn('解析流数据失败:', e, '原始数据:', line)
                    }
                  }
                }

                return readStream()
              })
            }

            return readStream()
          })
          .catch(error => {
            if (error.name !== 'AbortError') {
              console.error('流式请求错误:', error)
              commit('SET_STREAMING', { active: false, messageId: null, answer: '' })
              commit('SET_SENDING', false)
              commit('UPDATE_MESSAGE', {
                id: newAiMessageId,
                updates: {
                  answer: '抱歉，回答生成失败，请重试',
                  isStreaming: false,
                  done: true,
                  error: true
                }
              })
              reject(error)
            }
          })

      } catch (error) {
        commit('SET_SENDING', false)
        commit('SET_STREAMING', { active: false, messageId: null, answer: '' })
        reject(error)
      }
    })
  },

  async selectChat({ commit }, chatId) {
    const response = await knowledgeApi.getHistoryChat(chatId)
    if (response.code !== 200) {
      throw new Error(response.msg || '获取历史记录详情失败')
    }

    const chatDetail = formatHistoryItem(response.data)
    commit('CLEAR_MESSAGES')

    commit('ADD_MESSAGE', {
      type: 'user',
      answer: chatDetail.query,
      timestamp: chatDetail.timestamp
    })

    commit('ADD_MESSAGE', {
      type: 'assistant',
      answer: chatDetail.answer,
      timestamp: chatDetail.timestamp,
      liked: chatDetail.liked,
      historyId: chatDetail.id,
      done: true
    })
  },

  stopStreamChat({ commit, state }) {
    const messageId = state.streaming.messageId

    if (state.abortController) {
      state.abortController.abort()
      state.abortController = null
    }

    commit('SET_STREAMING', { active: false, messageId: null, answer: '' })
    commit('SET_SENDING', false)

    if (messageId) {
      commit('UPDATE_MESSAGE', {
        id: messageId,
        updates: { isStreaming: false, done: true }
      })
    }
  },

  async initializeForFileChat({ commit }, { knowledgeName, files }) {
    const knowledge = { id: knowledgeName, label: knowledgeName }

    commit('SET_CURRENT_KNOWLEDGE', knowledge)
    commit('SET_ACTIVE_FILES', files || [])
    commit('CLEAR_MESSAGES')
    commit('RESET_STREAMING')
    commit('SET_SENDING', false)
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
